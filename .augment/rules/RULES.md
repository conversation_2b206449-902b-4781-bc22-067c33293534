---
type: "always_apply"
---

# NS Shop - Project Rules & Architecture Documentation

## 🎯 Project Overview

NS Shop là một nền tảng thương mại điện tử thời trang hiện đại được xây dựng với Next.js 15, c<PERSON> giao diện khách hàng và bảng điều khiển quản trị. Dự án nhấn mạnh thiết kế thời trang với bảng màu hồng/magenta và hiệu ứng animation mượt mà.

## 🏗️ Kiến trúc & Tech Stack

### Core Technologies

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript (strict mode)
- **Styling**: Tailwind CSS v4 với custom fashion theme
- **Database**: PostgreSQL với Prisma ORM
- **UI Components**: Radix UI primitives
- **Icons**: Lucide React
- **Animation**: Framer Motion
- **Form Handling**: React Hook Form + Zod validation
- **Authentication**: NextAuth.js
- **State Management**: React Context + Zustand
- **Testing**: Jest + React Testing Library + Playwright

### Key Dependencies

- **@radix-ui/react-\***: UI component primitives
- **framer-motion**: Animation library
- **react-hook-form**: Form handling
- **zod**: Schema validation
- **prisma**: Database ORM
- **next-auth**: Authentication
- **tailwindcss**: Styling framework
- **lucide-react**: Icon library
- **sonner**: Toast notifications
- **recharts**: Data visualization

## 📋 Development Rules

### Core Rules

- Khi tạo doc luôn lưu vào thư mục `docs/`
- Khi test trên website, sử dụng `http://localhost:6002` (không cần run project)
- Sau khi hoàn thành task phải chạy `npx tsc` và fix errors
- Khi tạo/sửa schema database, luôn cập nhật repositories, services, models, dto liên quan
- Đảm bảo type-safe trong toàn bộ project
- Types/models/entities/dto lưu vào `src/app/models/` hoặc `src/app/dto/`
- Đảm bảo type-safe theo luồng từ backend đến frontend
- Hạn chế dùng type casting như "as"
- Ưu tiên quản lý state thông qua context/hooks

### Code Quality Standards

- **TypeScript strict mode** enabled
- **ESLint** với custom rules configuration
- **Prettier** cho code formatting
- Always run `npx tsc` trước khi commit
- Maintain 80%+ test coverage cho critical paths

## 🏛️ Architecture Patterns

### 1. Dependency Injection Pattern

- Custom DI container (`src/app/api/di-container.ts`)
- Services và repositories được register trong `di-setup.ts`
- Resolve dependencies tự động
- Singleton pattern cho shared instances

### 2. Repository Pattern

- Base repository với common CRUD operations
- Specific repositories extend từ BaseRepository
- Sử dụng Prisma client thông qua base class
- Built-in pagination và search functionality

### 3. Service Layer Architecture

- Business logic separation trong services
- Services inject repositories thông qua DI
- Data transformation giữa layers
- Business rules và validation handling

### 4. DTO Pattern với Zod Validation

- Request/response objects với type safety
- Zod schemas cho validation
- Transform functions giữa models và DTOs
- Consistent API response format

## 📁 Project Structure

```
ns-shop/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API routes với DI pattern
│   │   │   ├── di-container.ts    # DI container setup
│   │   │   ├── di-setup.ts        # Dependencies registration
│   │   │   ├── repositories/      # Data access layer
│   │   │   └── services/          # Business logic layer
│   │   ├── admin/             # Admin panel pages
│   │   ├── models/            # Data models và entities
│   │   ├── dto/               # Data Transfer Objects
│   │   ├── auth/              # Authentication pages
│   │   ├── products/          # Product pages
│   │   ├── categories/        # Category pages
│   │   ├── cart/              # Shopping cart
│   │   ├── checkout/          # Checkout process
│   │   ├── orders/            # Order management
│   │   ├── profile/           # User profile
│   │   └── search/            # Search functionality
│   ├── components/            # React components
│   │   ├── admin/            # Admin-specific components
│   │   ├── shop/             # Shop-specific components
│   │   ├── ui/               # Reusable UI components
│   │   ├── layout/           # Layout components
│   │   ├── shared/           # Shared components
│   │   └── providers/        # Context providers
│   ├── contexts/             # React contexts
│   │   ├── AdminAuthContext.tsx
│   │   ├── cart-context.tsx
│   │   ├── user-context.tsx
│   │   └── search-context.tsx
│   ├── hooks/                # Custom hooks
│   │   ├── admin/            # Admin-specific hooks
│   │   ├── use-products.ts
│   │   ├── use-cart.ts
│   │   ├── use-search.ts
│   │   └── use-user.ts
│   ├── lib/                  # Utilities và services
│   │   ├── api/              # API client utilities
│   │   ├── auth/             # Authentication utilities
│   │   ├── cache/            # Caching utilities
│   │   ├── email/            # Email service
│   │   ├── utils/            # General utilities
│   │   └── prisma.ts         # Prisma client
│   ├── types/                # TypeScript type definitions
│   └── middleware.ts         # Next.js middleware
├── docs/                     # Project documentation
├── tests/                    # Test files
│   ├── e2e/                 # Playwright E2E tests
│   ├── __mocks__/           # Jest mocks
│   ├── helpers/             # Test utilities
│   └── setup.ts             # Test setup
├── prisma/                   # Database schema và migrations
│   ├── schema.prisma        # Database schema
│   ├── migrations/          # Migration files
│   └── seeders/             # Database seeders
├── public/                   # Static assets
└── scripts/                  # Build và utility scripts
```

## 🔄 Data Flow Patterns

### API Request Flow

```
Request → Middleware → API Route → DI Container → Service → Repository → Database
Response ← DTO Transform ← Service Response ← Repository Response ← API Route
```

### Frontend Data Flow

```
Component → Hook → API Client → Backend API → Database
Component ← Context/State ← Hook ← API Response ← Backend API
```

### Authentication Flow

```
Login → NextAuth → JWT Token → Session → Protected Routes
Admin → Separate Auth → Admin Session → Admin Routes
```

## 🗄️ Database Architecture

### Core Models

#### User Management

- **User**: Customer accounts với profile information
- **AdminUser**: Admin accounts với role-based permissions
- **Address**: User shipping/billing addresses

#### Product Catalog

- **Product**: Core product information với variants
- **Category**: Hierarchical category structure
- **Brand**: Product brands với logos
- **Attribute**: Product attributes (size, color, etc.)
- **Inventory**: Stock management và tracking

#### E-commerce Operations

- **Order**: Order management với status tracking
- **OrderItem**: Individual items trong orders
- **Cart**: Shopping cart functionality
- **CartItem**: Items trong shopping cart
- **Review**: Product reviews và ratings
- **Wishlist**: User wishlist functionality

#### Content Management

- **Post**: Blog posts và content
- **Page**: Static pages (About, FAQ, etc.)
- **Media**: File upload và management
- **Menu**: Navigation menu structure

#### System Features

- **Setting**: Application configuration
- **Notification**: User notifications
- **AuditLog**: Admin action tracking
- **Contact**: Contact form submissions

### Database Relationships

```
User 1:N Order
User 1:1 Cart
User 1:N Address
User 1:N Review
User 1:N WishlistItem

Product 1:N OrderItem
Product 1:N CartItem
Product 1:N Review
Product N:M Category
Product N:1 Brand

Order 1:N OrderItem
Cart 1:N CartItem
```

## 🧪 Testing Architecture

### Testing Stack

- **Jest**: Unit testing framework
- **React Testing Library**: Component testing
- **Playwright**: End-to-end testing
- **MSW**: API mocking
- **Testing Utilities**: Custom test helpers

### Test Structure

```
tests/
├── e2e/                    # Playwright E2E tests
│   ├── admin/             # Admin panel tests
│   ├── shop/              # Customer-facing tests
│   └── global-setup.ts    # E2E test setup
├── __mocks__/             # Jest mocks
│   ├── prisma.ts          # Database mocks
│   └── next-auth.ts       # Auth mocks
├── helpers/               # Test utilities
│   ├── test-utils.ts      # Common test helpers
│   └── mock-data.ts       # Mock data factories
├── fixtures/              # Test data files
└── setup.ts               # Jest setup configuration
```

### Testing Patterns

#### Unit Testing

- **Hooks**: Custom hooks với mock dependencies
- **Services**: Business logic với mock repositories
- **Utilities**: Pure functions và helpers
- **Components**: UI components với mock contexts

#### Integration Testing

- **API Routes**: End-to-end API testing
- **Database**: Repository pattern testing
- **Authentication**: Auth flow testing

#### E2E Testing

- **User Flows**: Complete user journeys
- **Admin Workflows**: Admin panel functionality
- **Cross-browser**: Multiple browser testing

### Test Configuration

#### Jest Configuration

- **Environment**: jsdom cho React components
- **Setup Files**: Global mocks và polyfills
- **Coverage**: 80%+ coverage requirement
- **Module Mapping**: Path aliases support

#### Playwright Configuration

- **Base URL**: `http://localhost:6002`
- **Browsers**: Chrome, Firefox, Safari
- **Mobile**: Responsive testing
- **Screenshots**: Failure capture

## 🚀 Build & Deployment

### Development Environment

#### Scripts

```bash
yarn dev              # Development server (port 6002)
yarn build            # Production build với Turbopack
yarn start            # Production server
yarn lint             # TypeScript type checking
yarn test             # Jest unit tests
yarn test:e2e         # Playwright E2E tests
```

#### Database Scripts

```bash
yarn p:m              # Prisma migrate dev
yarn p:m:r            # Prisma migrate reset
yarn p:s              # Prisma Studio
yarn db:seed          # Database seeding
yarn db:reset         # Reset và seed database
```

#### Docker Services

```bash
yarn dup              # Start PostgreSQL, Redis, MinIO
```

### Build Configuration

#### Next.js Configuration

- **Turbopack**: Fast build system
- **Image Optimization**: Unoptimized for development
- **Strict Mode**: Disabled for compatibility
- **External Domains**: Image sources allowed

#### TypeScript Configuration

- **Strict Mode**: Enabled
- **Target**: ES2022
- **Module Resolution**: Bundler
- **Path Mapping**: `@/*` aliases

#### Tailwind Configuration

- **Dark Mode**: Class-based
- **Content**: All source files
- **Custom Theme**: Fashion-focused colors
- **Animations**: Tailwind Animate plugin

### Environment Variables

#### Required Variables

```env
DATABASE_URL          # PostgreSQL connection
NEXTAUTH_URL         # NextAuth base URL
NEXTAUTH_SECRET      # NextAuth secret key
JWT_SECRET           # JWT signing secret
```

#### Optional Variables

```env
EMAIL_PROVIDER       # Email service provider
SENDGRID_API_KEY     # SendGrid configuration
AWS_*                # AWS SES configuration
MINIO_*              # Object storage configuration
REDIS_URL            # Redis cache URL
```

### Deployment Targets

#### Vercel (Recommended)

- **Framework**: Next.js preset
- **Environment**: Production variables
- **Database**: External PostgreSQL
- **Build**: Automatic deployments

#### Docker

- **Base Image**: Node.js Alpine
- **Multi-stage**: Build và runtime separation
- **Services**: PostgreSQL, Redis, MinIO
- **Volumes**: Persistent data storage

## 🔧 Development Workflow

### Code Quality Pipeline

1. **Development**
   - TypeScript strict mode
   - ESLint real-time checking
   - Prettier auto-formatting

2. **Pre-commit**
   - `npx tsc` type checking
   - ESLint validation
   - Test execution

3. **Testing**
   - Unit tests với Jest
   - Integration tests
   - E2E tests với Playwright

4. **Build**
   - Next.js production build
   - Bundle optimization
   - Asset optimization

### Database Workflow

1. **Schema Changes**
   - Update `prisma/schema.prisma`
   - Generate migration: `yarn p:m`
   - Update related models/DTOs
   - Update repositories/services

2. **Data Seeding**
   - Factory pattern cho test data
   - Consistent seed data
   - Environment-specific seeding

### API Development

1. **Create Repository**
   - Extend BaseRepository
   - Implement specific methods
   - Register trong DI container

2. **Create Service**
   - Inject repository dependencies
   - Implement business logic
   - Handle data transformation

3. **Create API Route**
   - Resolve service từ DI
   - Handle request/response
   - Implement error handling

4. **Create DTOs**
   - Define Zod schemas
   - Create transform functions
   - Ensure type safety

### Frontend Development

1. **Component Creation**
   - Use TypeScript interfaces
   - Implement proper props typing
   - Follow component composition patterns

2. **Hook Development**
   - Custom hooks cho data fetching
   - State management với context
   - Error handling và loading states

3. **Context Management**
   - Global state với React Context
   - Provider composition
   - Type-safe context consumers

4. **Styling**
   - Tailwind CSS classes
   - Component variants với CVA
   - Responsive design patterns

## 🔒 Security & Performance

### Security Measures

- **Authentication**: NextAuth.js với JWT tokens
- **Authorization**: Role-based access control
- **Input Validation**: Zod schemas cho all inputs
- **SQL Injection**: Prisma ORM protection
- **XSS Protection**: React built-in protection
- **CSRF Protection**: NextAuth.js built-in

### Performance Optimization

- **Code Splitting**: Next.js automatic splitting
- **Image Optimization**: Next.js Image component
- **Caching**: Redis cho session và data caching
- **Database**: Connection pooling và query optimization
- **Bundle Size**: Tree shaking và dynamic imports
- **SEO**: Meta tags và structured data

### Monitoring & Logging

- **Error Tracking**: Console logging và error boundaries
- **Performance**: Next.js built-in analytics
- **Audit Logging**: Admin action tracking
- **Database**: Query performance monitoring

## 📚 Documentation Standards

### Code Documentation

- **TypeScript**: Comprehensive type definitions
- **JSDoc**: Function và class documentation
- **README**: Project setup và usage
- **API**: Endpoint documentation

### Architecture Documentation

- **Database Schema**: ER diagrams và relationships
- **API Design**: Request/response examples
- **Component Library**: Storybook documentation
- **Testing**: Test strategy và coverage reports

### User Documentation

- **Admin Guide**: Admin panel usage
- **User Manual**: Customer-facing features
- **Developer Guide**: Setup và contribution
- **Deployment**: Production deployment guide
