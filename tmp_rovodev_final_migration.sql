-- Final Migration Script for Post Module Consistency
-- This script should be run after updating Prisma schema

-- 1. Add new columns to posts table
ALTER TABLE posts 
ADD COLUMN IF NOT EXISTS comment_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS published_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS meta_title TEXT,
ADD COLUMN IF NOT EXISTS meta_description TEXT,
ADD COLUMN IF NOT EXISTS keywords TEXT[] DEFAULT '{}',
ADD COLUMN IF NOT EXISTS metadata JSONB;

-- 2. Update existing posts to have default values
UPDATE posts 
SET comment_count = 0 
WHERE comment_count IS NULL;

UPDATE posts 
SET keywords = '{}' 
WHERE keywords IS NULL;

-- 3. Set published_at for already published posts
UPDATE posts 
SET published_at = created_at 
WHERE status = 'PUBLISHED' AND published_at IS NULL;

-- 4. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_posts_status ON posts(status);
CREATE INDEX IF NOT EXISTS idx_posts_featured ON posts(featured);
CREATE INDEX IF NOT EXISTS idx_posts_published_at ON posts(published_at);
CREATE INDEX IF NOT EXISTS idx_posts_author_id ON posts(author_id);
CREATE INDEX IF NOT EXISTS idx_posts_category_id ON posts(category_id);
CREATE INDEX IF NOT EXISTS idx_posts_slug ON posts(slug);
CREATE INDEX IF NOT EXISTS idx_posts_keywords ON posts USING GIN(keywords);
CREATE INDEX IF NOT EXISTS idx_posts_tags ON posts USING GIN(tags);

-- 5. Update view_count for existing posts (optional)
UPDATE posts 
SET view_count = FLOOR(RANDOM() * 100) + 1
WHERE view_count = 0 AND status = 'PUBLISHED';

-- 6. Verify data integrity
DO $$
BEGIN
    -- Check for posts without authors
    IF EXISTS (SELECT 1 FROM posts WHERE author_id NOT IN (SELECT id FROM admin_users)) THEN
        RAISE NOTICE 'Warning: Found posts with invalid author_id';
    END IF;
    
    -- Check for posts with invalid categories
    IF EXISTS (SELECT 1 FROM posts WHERE category_id IS NOT NULL AND category_id NOT IN (SELECT id FROM categories)) THEN
        RAISE NOTICE 'Warning: Found posts with invalid category_id';
    END IF;
    
    -- Check for posts with invalid featured images
    IF EXISTS (SELECT 1 FROM posts WHERE featured_image_id IS NOT NULL AND featured_image_id NOT IN (SELECT id FROM media)) THEN
        RAISE NOTICE 'Warning: Found posts with invalid featured_image_id';
    END IF;
END $$;