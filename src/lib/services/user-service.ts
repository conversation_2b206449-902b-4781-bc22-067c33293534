"use client";

import { api } from "@/lib/api-client";
import { User, Address, ApiResponse } from "@/types";
import { Gender } from "@/app/models/common.model";

export interface UserProfileData {
  name: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: Gender;
  avatarId?: string;
}

export interface AddressData {
  fullName: string;
  phone: string;
  address: string;
  ward: string;
  district: string;
  province: string;
  isDefault?: boolean;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export class UserService {
  private static readonly ENDPOINT = "/user";

  // Get current user profile
  static async getProfile(): Promise<ApiResponse<User>> {
    return api.getById<User>(`${this.ENDPOINT}/profile`, "");
  }

  // Update user profile
  static async updateProfile(
    data: UserProfileData
  ): Promise<ApiResponse<User>> {
    return api.update<User>(`${this.ENDPOINT}/profile`, "", data);
  }

  // Change password
  static async changePassword(
    data: ChangePasswordData
  ): Promise<ApiResponse<void>> {
    return api.create<void>(`${this.ENDPOINT}/change-password`, data);
  }

  // Upload avatar
  static async uploadAvatar(
    file: File
  ): Promise<ApiResponse<{ avatarUrl: string }>> {
    const formData = new FormData();
    formData.append("avatar", file);

    // Note: This would need special handling for file uploads
    // The api client would need to be modified to handle FormData
    const response = await fetch(`/api${this.ENDPOINT}/avatar`, {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error("Failed to upload avatar");
    }

    return response.json();
  }

  // Delete avatar
  static async deleteAvatar(): Promise<ApiResponse<void>> {
    return api.remove<void>(`${this.ENDPOINT}/avatar`, "");
  }

  // Address management
  static async getAddresses(): Promise<ApiResponse<Address[]>> {
    return api.getById<Address[]>(`${this.ENDPOINT}/addresses`, "");
  }

  static async addAddress(data: AddressData): Promise<ApiResponse<Address>> {
    return api.create<Address>(`${this.ENDPOINT}/addresses`, data);
  }

  static async updateAddress(
    id: string,
    data: Partial<AddressData>
  ): Promise<ApiResponse<Address>> {
    return api.update<Address>(`${this.ENDPOINT}/addresses`, id, data);
  }

  static async deleteAddress(id: string): Promise<ApiResponse<void>> {
    return api.remove<void>(`${this.ENDPOINT}/addresses`, id);
  }

  static async setDefaultAddress(id: string): Promise<ApiResponse<Address>> {
    return api.create<Address>(
      `${this.ENDPOINT}/addresses/${id}/set-default`,
      {}
    );
  }

  // Wishlist management
  static async getWishlist(): Promise<ApiResponse<any[]>> {
    return api.getById<any[]>(`${this.ENDPOINT}/wishlist`, "");
  }

  static async addToWishlist(productId: string): Promise<ApiResponse<void>> {
    return api.create<void>(`${this.ENDPOINT}/wishlist`, { productId });
  }

  static async removeFromWishlist(
    productId: string
  ): Promise<ApiResponse<void>> {
    return api.remove<void>(`${this.ENDPOINT}/wishlist`, productId);
  }

  static async isInWishlist(productId: string): Promise<boolean> {
    try {
      const response = await api.getById<{ inWishlist: boolean }>(
        `${this.ENDPOINT}/wishlist/check`,
        productId
      );
      return response.data?.inWishlist || false;
    } catch {
      return false;
    }
  }

  // Account settings
  static async updateEmailPreferences(
    preferences: Record<string, boolean>
  ): Promise<ApiResponse<void>> {
    return api.update<void>(
      `${this.ENDPOINT}/preferences`,
      "email",
      preferences
    );
  }

  static async updateNotificationSettings(
    settings: Record<string, boolean>
  ): Promise<ApiResponse<void>> {
    return api.update<void>(
      `${this.ENDPOINT}/preferences`,
      "notifications",
      settings
    );
  }

  // Account deletion
  static async requestAccountDeletion(): Promise<ApiResponse<void>> {
    return api.create<void>(`${this.ENDPOINT}/delete-request`, {});
  }

  static async cancelAccountDeletion(): Promise<ApiResponse<void>> {
    return api.remove<void>(`${this.ENDPOINT}/delete-request`, "");
  }

  // Utility methods
  static getFullName(user: User): string {
    return user.name || "Người dùng";
  }

  static getDisplayName(user: User): string {
    const name = this.getFullName(user);
    return name.split(" ").slice(-1)[0]; // Get last name
  }

  static getAvatarUrl(user: User): string | null {
    return user.avatar?.url || null;
  }

  static getDefaultAddress(addresses: Address[]): Address | null {
    return addresses.find((addr) => addr.isDefault) || addresses[0] || null;
  }

  static formatAddress(address: Address): string {
    return `${address.address}, ${address.ward}, ${address.district}, ${address.province}`;
  }

  static validatePhone(phone: string): boolean {
    const phoneRegex = /^(\+84|84|0)[3|5|7|8|9][0-9]{8}$/;
    return phoneRegex.test(phone);
  }

  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static formatPhoneNumber(phone: string): string {
    // Format Vietnamese phone number
    const cleaned = phone.replace(/\D/g, "");

    if (cleaned.startsWith("84")) {
      return `+${cleaned}`;
    }

    if (cleaned.startsWith("0")) {
      return `+84${cleaned.slice(1)}`;
    }

    return `+84${cleaned}`;
  }

  static getGenderLabel(gender?: Gender): string {
    switch (gender) {
      case "MALE":
        return "Nam";
      case "FEMALE":
        return "Nữ";
      case "OTHER":
        return "Khác";
      default:
        return "Chưa xác định";
    }
  }

  static calculateAge(dateOfBirth?: Date | string): number | null {
    if (!dateOfBirth) return null;

    const birth = new Date(dateOfBirth);
    const today = new Date();
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birth.getDate())
    ) {
      age--;
    }

    return age;
  }

  static isProfileComplete(user: User): boolean {
    return !!(user.name && user.email && user.phone);
  }

  static getProfileCompletionPercentage(user: User): number {
    const fields = [
      user.name,
      user.email,
      user.phone,
      user.dateOfBirth,
      user.gender,
      user.avatar,
    ];

    const completedFields = fields.filter(Boolean).length;
    return Math.round((completedFields / fields.length) * 100);
  }
}

export default UserService;
