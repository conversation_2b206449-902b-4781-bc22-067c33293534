"use client";

import Link from "next/link";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Star, ArrowRight, Loader2 } from "lucide-react";
import { BrandLogo } from "@/components/ui/client-image";
import { useState } from "react";
import { useFeaturedBrands } from "@/hooks";

const _mockBrands = [
  {
    id: 1,
    name: "Nike",
    logo: "/images/brands/nike.png",
    description: "Thương hiệu thể thao hàng đầu thế giới",
    productCount: 156,
    rating: 4.8,
    isPartner: true,
    category: "Thể thao",
    established: 1971,
    country: "USA",
  },
  {
    id: 2,
    name: "Adidas",
    logo: "/images/brands/adidas.png",
    description: "Thương hiệu thể thao nổi tiếng từ Đức",
    productCount: 134,
    rating: 4.7,
    isPartner: true,
    category: "Thể thao",
    established: 1949,
    country: "Germany",
  },
  {
    id: 3,
    name: "<PERSON><PERSON>",
    logo: "/images/brands/zara.png",
    description: "Thời trang nhanh từ Tây Ban Nha",
    productCount: 289,
    rating: 4.6,
    isPartner: true,
    category: "Thời trang",
    established: 1975,
    country: "Spain",
  },
  {
    id: 4,
    name: "H&M",
    logo: "/images/brands/hm.png",
    description: "Thời trang bền vững và phong cách",
    productCount: 245,
    rating: 4.5,
    isPartner: true,
    category: "Thời trang",
    established: 1947,
    country: "Sweden",
  },
  {
    id: 5,
    name: "Uniqlo",
    logo: "/images/brands/uniqlo.png",
    description: "Thời trang tối giản từ Nhật Bản",
    productCount: 178,
    rating: 4.7,
    isPartner: true,
    category: "Thời trang",
    established: 1984,
    country: "Japan",
  },
  {
    id: 6,
    name: "Louis Vuitton",
    logo: "/images/brands/lv.png",
    description: "Thương hiệu xa xỉ hàng đầu",
    productCount: 89,
    rating: 4.9,
    isPartner: false,
    category: "Xa xỉ",
    established: 1854,
    country: "France",
  },
  {
    id: 7,
    name: "Gucci",
    logo: "/images/brands/gucci.png",
    description: "Thời trang cao cấp từ Italy",
    productCount: 67,
    rating: 4.8,
    isPartner: false,
    category: "Xa xỉ",
    established: 1921,
    country: "Italy",
  },
  {
    id: 8,
    name: "Chanel",
    logo: "/images/brands/chanel.png",
    description: "Biểu tượng thời trang nữ tính",
    productCount: 45,
    rating: 4.9,
    isPartner: false,
    category: "Xa xỉ",
    established: 1910,
    country: "France",
  },
];

const categories = ["Tất cả", "Thể thao", "Thời trang", "Xa xỉ"];

export function BrandsSection() {
  const [selectedCategory, setSelectedCategory] = useState("Tất cả");
  const [hoveredBrand, setHoveredBrand] = useState<string | null>(null);
  
  const { brands, loading, error } = useFeaturedBrands(12);

  const filteredBrands =
    selectedCategory === "Tất cả"
      ? brands
      : brands.filter((brand) => brand.category === selectedCategory);

  if (loading) {
    return (
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold mb-4">
              Thương hiệu <span className="text-fashion-600">đối tác</span>
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Hợp tác với hơn 50 thương hiệu nổi tiếng thế giới để mang đến những
              sản phẩm chất lượng nhất
            </p>
          </div>
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-fashion-600" />
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-16 lg:py-24">
        <div className="container mx-auto px-4">
          <div className="text-center">
            <p className="text-muted-foreground">Có lỗi xảy ra khi tải thương hiệu</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-16 lg:py-24">
      <div className="container mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold mb-4">
            Thương hiệu <span className="text-fashion-600">đối tác</span>
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Hợp tác với hơn 50 thương hiệu nổi tiếng thế giới để mang đến những
            sản phẩm chất lượng nhất
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex justify-center mb-8">
          <div className="flex space-x-2 bg-muted/50 p-1 rounded-lg">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-all duration-300 ${
                  selectedCategory === category
                    ? "bg-fashion-500 text-white shadow-sm"
                    : "text-muted-foreground hover:text-foreground hover:bg-muted"
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Brands Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 lg:gap-8">
          {filteredBrands.map((brand) => (
            <Card
              key={brand.id}
              className="group cursor-pointer border-0 shadow-sm hover:shadow-lg transition-all duration-300 bg-white/80 backdrop-blur-sm"
              onMouseEnter={() => setHoveredBrand(brand.id)}
              onMouseLeave={() => setHoveredBrand(null)}
            >
              <Link href={`/brands/${brand.name.toLowerCase()}`}>
                <CardContent className="p-6 text-center">
                  {/* Brand Logo */}
                  <div className="relative mb-4">
                    <div className="w-16 h-16 mx-auto group-hover:scale-110 transition-transform duration-300">
                      <BrandLogo
                        src={typeof brand.logo === 'string' ? brand.logo : brand.logo?.url || ''}
                        alt={brand.logo && typeof brand.logo === 'object' ? brand.logo.alt || brand.name : brand.name}
                        width={64}
                        height={64}
                        className="rounded-lg"
                      />
                    </div>
                    {brand.isPartner && (
                      <Badge className="absolute -top-2 -right-2 bg-green-500 text-white text-xs">
                        Partner
                      </Badge>
                    )}
                  </div>

                  {/* Brand Info */}
                  <div className="space-y-2">
                    <h3 className="font-bold text-lg group-hover:text-fashion-600 transition-colors">
                      {brand.name}
                    </h3>

                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {brand.description}
                    </p>

                    {/* Rating */}
                    <div className="flex items-center justify-center gap-1">
                      <Star className="h-4 w-4 text-yellow-400 fill-current" />
                      <span className="text-sm font-medium">
                        {brand.rating || 0}
                      </span>
                    </div>

                    {/* Product Count */}
                    <div className="text-sm text-fashion-600 font-medium">
                      {brand.productCount || 0} sản phẩm
                    </div>

                    {/* Additional Info on Hover */}
                    {hoveredBrand === brand.id && (
                      <div className="mt-4 pt-4 border-t border-muted space-y-1 text-xs text-muted-foreground">
                        {brand.established && <div>Thành lập: {brand.established}</div>}
                        {brand.country && <div>Xuất xứ: {brand.country}</div>}
                      </div>
                    )}
                  </div>

                  {/* Hover Arrow */}
                  <div className="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <ArrowRight className="h-4 w-4 mx-auto text-fashion-500" />
                  </div>
                </CardContent>
              </Link>
            </Card>
          ))}
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-16 pt-16 border-t border-muted">
          <div className="text-center">
            <div className="text-3xl font-bold text-fashion-600 mb-2">50+</div>
            <div className="text-sm text-muted-foreground">
              Thương hiệu đối tác
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-fashion-600 mb-2">
              1000+
            </div>
            <div className="text-sm text-muted-foreground">
              Sản phẩm chính hãng
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-fashion-600 mb-2">100%</div>
            <div className="text-sm text-muted-foreground">
              Bảo hành chính hãng
            </div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-fashion-600 mb-2">24h</div>
            <div className="text-sm text-muted-foreground">Giao hàng nhanh</div>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center mt-12">
          <Link
            href="/brands"
            className="inline-flex items-center space-x-2 text-fashion-600 hover:text-fashion-700 font-medium transition-colors group"
          >
            <span>Xem tất cả thương hiệu</span>
            <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
          </Link>
        </div>
      </div>
    </section>
  );
}
