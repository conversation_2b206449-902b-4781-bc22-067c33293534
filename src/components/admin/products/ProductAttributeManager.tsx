"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { toast } from "sonner";
import {
  Tag,
  Plus,
  X,
  Palette,
  Ruler,
  Type,
  Hash,
  ToggleLeft,
  List,
  ListChecks,
  AlertCircle,
} from "lucide-react";
import {
  Attribute,
  AttributeType,
  ATTRIBUTE_TYPE_LABELS,
} from "@/types/attribute";
import { useAdminAttributes } from "@/hooks/admin/useAdminAttributes";

interface ProductAttribute {
  attributeId: string;
  attributeValueId: string;
}

interface ProductAttributeManagerProps {
  attributes: ProductAttribute[];
  onAttributesChange: (attributes: ProductAttribute[]) => void;
  className?: string;
}

const ATTRIBUTE_TYPE_ICONS: Record<AttributeType, React.ComponentType<any>> = {
  TEXT: Type,
  NUMBER: Hash,
  COLOR: Palette,
  SIZE: Ruler,
  BOOLEAN: ToggleLeft,
  SELECT: List,
  MULTI_SELECT: ListChecks,
};

export function ProductAttributeManager({
  attributes,
  onAttributesChange,
  className,
}: ProductAttributeManagerProps) {
  const { 
    attributes: availableAttributes, 
    loading, 
    fetchAttributes 
  } = useAdminAttributes();

  // Load available attributes
  useEffect(() => {
    fetchAttributes({ limit: 100 });
  }, [fetchAttributes]);

  const getAttributeById = (id: string) => {
    return availableAttributes.find((attr) => attr.id === id);
  };

  const getAttributeIcon = (type: AttributeType) => {
    const Icon = ATTRIBUTE_TYPE_ICONS[type];
    return <Icon className="h-4 w-4" />;
  };

  const handleAddAttribute = () => {
    const newAttribute: ProductAttribute = {
      attributeId: "",
      attributeValueId: "",
    };
    onAttributesChange([...attributes, newAttribute]);
  };

  const handleRemoveAttribute = (index: number) => {
    const newAttributes = attributes.filter((_, i) => i !== index);
    onAttributesChange(newAttributes);
  };

  const handleAttributeChange = (
    index: number,
    field: keyof ProductAttribute,
    value: string
  ) => {
    const newAttributes = attributes.map((attr, i) =>
      i === index ? { ...attr, [field]: value } : attr
    );
    onAttributesChange(newAttributes);
  };

  const validateAttributes = () => {
    const errors: string[] = [];

    // Check attributes completeness
    attributes.forEach((attr, index) => {
      if (attr.attributeId && !attr.attributeValueId) {
        errors.push(`Thuộc tính #${index + 1} chưa có giá trị`);
      }
      if (!attr.attributeId && attr.attributeValueId) {
        errors.push(`Thuộc tính #${index + 1} chưa chọn loại thuộc tính`);
      }
    });

    return errors;
  };

  const renderAttributeInput = (
    attribute: Attribute,
    value: string,
    onChange: (value: string) => void
  ) => {
    if (!attribute.values || attribute.values.length === 0) {
      // For attributes without predefined values (TEXT, NUMBER, BOOLEAN)
      switch (attribute.type) {
        case "TEXT":
          return (
            <Input
              value={value}
              onChange={(e) => onChange(e.target.value)}
              placeholder="Nhập giá trị..."
            />
          );
        case "NUMBER":
          return (
            <Input
              type="number"
              value={value}
              onChange={(e) => onChange(e.target.value)}
              placeholder="Nhập số..."
            />
          );
        case "BOOLEAN":
          return (
            <Switch
              checked={value === "true"}
              onCheckedChange={(checked) =>
                onChange(checked ? "true" : "false")
              }
            />
          );
        default:
          return (
            <Input
              value={value}
              onChange={(e) => onChange(e.target.value)}
              placeholder="Nhập giá trị..."
            />
          );
      }
    }

    // For attributes with predefined values
    return (
      <Select value={value} onValueChange={onChange}>
        <SelectTrigger>
          <SelectValue placeholder="Chọn giá trị..." />
        </SelectTrigger>
        <SelectContent>
          {attribute.values
            .sort((a, b) => a.sortOrder - b.sortOrder)
            .map((val) => (
              <SelectItem key={val.id} value={val.id}>
                {val.value}
              </SelectItem>
            ))}
        </SelectContent>
      </Select>
    );
  };

  const validationErrors = validateAttributes();

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <Tag className="h-5 w-5 mr-2" />
              Thuộc tính sản phẩm
            </div>
            {validationErrors.length > 0 && (
              <Badge variant="destructive" className="text-xs">
                <AlertCircle className="h-3 w-3 mr-1" />
                {validationErrors.length} lỗi
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Validation Errors */}
          {validationErrors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-2">
                <AlertCircle className="h-4 w-4 text-red-600" />
                <span className="text-sm font-medium text-red-800">
                  Cần khắc phục các lỗi sau:
                </span>
              </div>
              <ul className="text-sm text-red-700 space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index} className="flex items-start gap-2">
                    <span className="text-red-400 mt-1">•</span>
                    <span>{error}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}
          {/* Product Attributes */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Badge
                  variant="secondary"
                  className="text-xs bg-blue-100 text-blue-800 border-blue-200"
                >
                  Thuộc tính sản phẩm
                </Badge>
                {attributes.length > 0 && (
                  <span className="text-xs text-muted-foreground">
                    ({attributes.length} thuộc tính)
                  </span>
                )}
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddAttribute}
                disabled={loading}
                className="hover:bg-blue-50 hover:border-blue-300"
              >
                <Plus className="h-4 w-4 mr-2" />
                Thêm thuộc tính
              </Button>
            </div>

            {attributes.length > 0 ? (
              <div className="space-y-4">
                {attributes.map((attr, index) => {
                  const attribute = getAttributeById(attr.attributeId);

                  return (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-start gap-4">
                        <div className="flex-1 space-y-4">
                          <div>
                            <Label>Thuộc tính</Label>
                            <Select
                              value={attr.attributeId}
                              onValueChange={(value) =>
                                handleAttributeChange(
                                  index,
                                  "attributeId",
                                  value
                                )
                              }
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Chọn thuộc tính..." />
                              </SelectTrigger>
                              <SelectContent>
                                {availableAttributes.map((availableAttr) => (
                                  <SelectItem
                                    key={availableAttr.id}
                                    value={availableAttr.id}
                                  >
                                    <div className="flex items-center gap-2">
                                      {getAttributeIcon(availableAttr.type)}
                                      <span>{availableAttr.name}</span>
                                      <Badge
                                        variant="outline"
                                        className="text-xs"
                                      >
                                        {
                                          ATTRIBUTE_TYPE_LABELS[
                                            availableAttr.type
                                          ]
                                        }
                                      </Badge>
                                    </div>
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>

                          {attribute && (
                            <div>
                              <Label>Giá trị</Label>
                              {renderAttributeInput(
                                attribute,
                                attr.attributeValueId,
                                (value) =>
                                  handleAttributeChange(
                                    index,
                                    "attributeValueId",
                                    value
                                  )
                              )}
                            </div>
                          )}
                        </div>

                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRemoveAttribute(index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <div className="text-center py-8 text-muted-foreground">
                <Tag className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Chưa có thuộc tính nào</p>
                <p className="text-sm">Nhấn "Thêm thuộc tính" để bắt đầu</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
