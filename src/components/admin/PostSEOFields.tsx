/**
 * Post SEO Fields Component
 * Reusable component for SEO fields in post forms
 */

"use client";

import { Control, useFieldArray } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Plus, X, Tag as TagIcon } from "lucide-react";
import { useState } from "react";

interface PostSEOFieldsProps {
  control: Control<any>;
  register: any;
  watch: any;
  setValue: any;
}

export function PostSEOFields({ control, register, watch, setValue }: PostSEOFieldsProps) {
  const [newKeyword, setNewKeyword] = useState("");
  const keywords = watch("keywords") || [];

  const addKeyword = () => {
    if (newKeyword.trim() && !keywords.includes(newKeyword.trim())) {
      setValue("keywords", [...keywords, newKeyword.trim()]);
      setNewKeyword("");
    }
  };

  const removeKeyword = (index: number) => {
    const updatedKeywords = keywords.filter((_: any, i: number) => i !== index);
    setValue("keywords", updatedKeywords);
  };

  const handleKeywordKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addKeyword();
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TagIcon className="h-5 w-5" />
          SEO Settings
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Meta Title */}
        <div className="space-y-2">
          <Label htmlFor="metaTitle">Meta Title</Label>
          <Input
            id="metaTitle"
            placeholder="SEO title for search engines"
            {...register("metaTitle")}
          />
          <p className="text-sm text-muted-foreground">
            Recommended: 50-60 characters
          </p>
        </div>

        {/* Meta Description */}
        <div className="space-y-2">
          <Label htmlFor="metaDescription">Meta Description</Label>
          <Textarea
            id="metaDescription"
            placeholder="Brief description for search engines"
            rows={3}
            {...register("metaDescription")}
          />
          <p className="text-sm text-muted-foreground">
            Recommended: 150-160 characters
          </p>
        </div>

        {/* Keywords */}
        <div className="space-y-2">
          <Label htmlFor="keywords">Keywords</Label>
          <div className="flex gap-2">
            <Input
              value={newKeyword}
              onChange={(e) => setNewKeyword(e.target.value)}
              onKeyPress={handleKeywordKeyPress}
              placeholder="Add keyword and press Enter"
            />
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addKeyword}
              disabled={!newKeyword.trim()}
            >
              <Plus className="h-4 w-4" />
            </Button>
          </div>
          
          {/* Keywords Display */}
          {keywords.length > 0 && (
            <div className="flex flex-wrap gap-2 mt-2">
              {keywords.map((keyword: string, index: number) => (
                <Badge key={index} variant="secondary" className="flex items-center gap-1">
                  {keyword}
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="h-auto p-0 hover:bg-transparent"
                    onClick={() => removeKeyword(index)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
            </div>
          )}
          <p className="text-sm text-muted-foreground">
            Add relevant keywords for better SEO
          </p>
        </div>

        {/* Publish Date */}
        <div className="space-y-2">
          <Label htmlFor="publishedAt">Publish Date</Label>
          <Input
            id="publishedAt"
            type="datetime-local"
            {...register("publishedAt")}
          />
          <p className="text-sm text-muted-foreground">
            Leave empty to publish immediately when status is set to Published
          </p>
        </div>
      </CardContent>
    </Card>
  );
}