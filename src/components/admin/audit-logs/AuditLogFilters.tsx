"use client";

import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Button,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Label,
} from "@/components/ui";
import { X, Search, RotateCcw } from "lucide-react";
import { useAdminAuditLogs } from "@/hooks/admin/useAdminAuditLogs";

import type { AuditLogFilters } from "@/hooks/admin/useAdminAuditLogs";

interface AuditLogFiltersProps {
  filters: AuditLogFilters;
  onFiltersChange: (filters: AuditLogFilters) => void;
  onClose: () => void;
}

export function AuditLogFilters({
  filters,
  onFiltersChange,
  onClose,
}: AuditLogFiltersProps) {
  const [localFilters, setLocalFilters] = useState(filters);
  const { filterOptions, fetchFilterOptions } = useAdminAuditLogs();
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadFilterOptions = async () => {
      setLoading(true);
      await fetchFilterOptions();
      setLoading(false);
    };
    loadFilterOptions();
  }, [fetchFilterOptions]);

  const handleInputChange = (field: string, value: string) => {
    setLocalFilters((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleApplyFilters = () => {
    onFiltersChange(localFilters);
  };

  const handleResetFilters = () => {
    const resetFilters: AuditLogFilters = {
      page: 1,
      limit: 20,
      action: "",
      resource: "",
      adminId: "",
      startDate: "",
      endDate: "",
      search: "",
      sortBy: "createdAt",
      sortOrder: "desc",
    };
    setLocalFilters(resetFilters);
    onFiltersChange(resetFilters);
  };

  const formatDateForInput = (dateString: string) => {
    if (!dateString) return "";
    return new Date(dateString).toISOString().split("T")[0];
  };

  const handleDateChange = (field: string, value: string) => {
    const date = value ? new Date(value).toISOString() : "";
    handleInputChange(field, date);
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-lg">Bộ lọc Audit Logs</CardTitle>
        <Button variant="ghost" size="sm" onClick={onClose}>
          <X className="h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="space-y-2">
          <Label htmlFor="search">Tìm kiếm</Label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              id="search"
              placeholder="Tìm kiếm trong action, resource, description, admin..."
              value={localFilters.search}
              onChange={(e) => handleInputChange("search", e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Action Filter */}
          <div className="space-y-2">
            <Label htmlFor="action">Hành động</Label>
            <Select
              value={localFilters.action || "all"}
              onValueChange={(value) =>
                handleInputChange("action", value === "all" ? "" : value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Chọn hành động" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                {filterOptions.actions.map((action) => (
                  <SelectItem key={action} value={action}>
                    {action}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Resource Filter */}
          <div className="space-y-2">
            <Label htmlFor="resource">Tài nguyên</Label>
            <Select
              value={localFilters.resource || "all"}
              onValueChange={(value) =>
                handleInputChange("resource", value === "all" ? "" : value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Chọn tài nguyên" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                {filterOptions.resources.map((resource) => (
                  <SelectItem key={resource} value={resource}>
                    {resource}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Admin Filter */}
          <div className="space-y-2">
            <Label htmlFor="admin">Admin</Label>
            <Select
              value={localFilters.adminId || "all"}
              onValueChange={(value) =>
                handleInputChange("adminId", value === "all" ? "" : value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Chọn admin" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tất cả</SelectItem>
                {filterOptions.admins.map((admin) => (
                  <SelectItem key={admin.id} value={admin.id}>
                    {admin.name} ({admin.email})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Start Date */}
          <div className="space-y-2">
            <Label htmlFor="startDate">Từ ngày</Label>
            <Input
              id="startDate"
              type="date"
              value={formatDateForInput(localFilters.startDate)}
              onChange={(e) => handleDateChange("startDate", e.target.value)}
            />
          </div>

          {/* End Date */}
          <div className="space-y-2">
            <Label htmlFor="endDate">Đến ngày</Label>
            <Input
              id="endDate"
              type="date"
              value={formatDateForInput(localFilters.endDate)}
              onChange={(e) => handleDateChange("endDate", e.target.value)}
            />
          </div>

          {/* Sort By */}
          <div className="space-y-2">
            <Label htmlFor="sortBy">Sắp xếp theo</Label>
            <Select
              value={localFilters.sortBy}
              onValueChange={(value) => handleInputChange("sortBy", value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="createdAt">Thời gian</SelectItem>
                <SelectItem value="action">Hành động</SelectItem>
                <SelectItem value="resource">Tài nguyên</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Sort Order */}
          <div className="space-y-2">
            <Label htmlFor="sortOrder">Thứ tự</Label>
            <Select
              value={localFilters.sortOrder}
              onValueChange={(value) => handleInputChange("sortOrder", value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="desc">Giảm dần</SelectItem>
                <SelectItem value="asc">Tăng dần</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Limit */}
          <div className="space-y-2">
            <Label htmlFor="limit">Số lượng/trang</Label>
            <Select
              value={localFilters.limit.toString()}
              onValueChange={(value) => handleInputChange("limit", value)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="20">20</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-4 border-t">
          <Button
            variant="outline"
            onClick={handleResetFilters}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Đặt lại
          </Button>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={onClose}>
              Hủy
            </Button>
            <Button onClick={handleApplyFilters}>
              <Search className="h-4 w-4 mr-2" />
              Áp dụng bộ lọc
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
