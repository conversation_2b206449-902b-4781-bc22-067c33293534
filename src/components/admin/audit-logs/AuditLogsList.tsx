"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { vi } from "date-fns/locale";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Badge,
  Button,
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Progress,
} from "@/components/ui";
import {
  Eye,
  Download,
  RefreshCw,
  ChevronLeft,
  ChevronRight,
  Filter,
} from "lucide-react";
import { AuditLogFilters } from "./AuditLogFilters";
import { AuditLogDetail } from "./AuditLogDetail";
import { useAdminAuditLogs } from "@/hooks/admin/useAdminAuditLogs";
import type { AuditLogFilters as AuditLogFiltersType } from "@/hooks/admin/useAdminAuditLogs";

interface AuditLog {
  id: string;
  action: string;
  resource: string;
  resourceId?: string;
  description?: string;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
  admin: {
    id: string;
    name: string;
    email: string;
    avatar?: string;
    role: string;
  };
  oldValues?: any;
  newValues?: any;
}

interface AuditLogsResponse {
  data: AuditLog[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  };
  filters: any;
}

interface AuditLogsListProps {
  initialData?: AuditLogsResponse;
}

export function AuditLogsList({ initialData }: AuditLogsListProps) {
  const [data, setData] = useState<AuditLogsResponse | null>(
    initialData || null
  );
  const [selectedLog, setSelectedLog] = useState<AuditLog | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<AuditLogFiltersType>({
    page: 1,
    limit: 20,
    action: "",
    resource: "",
    adminId: "",
    startDate: "",
    endDate: "",
    search: "",
    sortBy: "createdAt",
    sortOrder: "desc",
  });
  
  const { auditLogs, loading, totalCount, fetchAuditLogs, exportAuditLogs } = useAdminAuditLogs();
  const [error, setError] = useState<string | null>(null);

  const fetchData = async (newFilters = filters) => {
    setError(null);
    const result = await fetchAuditLogs(newFilters);
    if (!result.success) {
      setError("Failed to fetch audit logs");
    } else {
      // Update data format to match existing interface
      setData({
        data: auditLogs,
        pagination: {
          page: newFilters.page,
          limit: newFilters.limit,
          totalCount,
          totalPages: Math.ceil(totalCount / newFilters.limit),
          hasNextPage: newFilters.page < Math.ceil(totalCount / newFilters.limit),
          hasPrevPage: newFilters.page > 1,
        },
        filters: newFilters,
      });
    }
  };

  useEffect(() => {
    if (!initialData) {
      fetchData();
    }
  }, []);

  const handleFilterChange = (newFilters: AuditLogFiltersType) => {
    const updatedFilters = { ...newFilters, page: 1 };
    setFilters(updatedFilters);
    fetchData(updatedFilters);
  };

  const handlePageChange = (page: number) => {
    const updatedFilters = { ...filters, page };
    setFilters(updatedFilters);
    fetchData(updatedFilters);
  };

  const [exportProgress, setExportProgress] = useState<number | null>(null);

  const handleExport = async (format: "csv" | "excel") => {
    setExportProgress(0);
    
    const result = await exportAuditLogs(filters);
    
    if (result.success) {
      setExportProgress(100);
      // Hide progress after 1 second
      setTimeout(() => setExportProgress(null), 1000);
    } else {
      setExportProgress(null);
      setError("Export failed");
    }
  };

  const getActionBadgeVariant = (action: string) => {
    if (action.includes("CREATE")) return "default";
    if (action.includes("UPDATE")) return "secondary";
    if (action.includes("DELETE")) return "destructive";
    if (action.includes("FAILED")) return "destructive";
    return "outline";
  };

  const getResourceIcon = (_resource: string) => {
    // You can add specific icons for different resources
    return "📄";
  };

  if (loading && !data) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-500 mb-4">{error}</p>
            <Button onClick={() => fetchData()}>Thử lại</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Audit Logs</h2>
          <p className="text-muted-foreground">
            Theo dõi tất cả hoạt động của admin trong hệ thống
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" />
            Bộ lọc
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport("csv")}
            disabled={exportProgress !== null}
          >
            <Download className="h-4 w-4 mr-2" />
            Export CSV
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport("excel")}
            disabled={exportProgress !== null}
          >
            <Download className="h-4 w-4 mr-2" />
            Export Excel
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => fetchData()}
            disabled={loading}
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${loading ? "animate-spin" : ""}`}
            />
            Làm mới
          </Button>
        </div>
      </div>

      {/* Export Progress */}
      {exportProgress !== null && (
        <Card>
          <CardContent className="p-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">
                  Đang xuất dữ liệu...
                </span>
                <span className="text-sm text-muted-foreground">
                  {exportProgress}%
                </span>
              </div>
              <Progress value={exportProgress} className="w-full" />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      {showFilters && (
        <AuditLogFilters
          filters={filters}
          onFiltersChange={handleFilterChange}
          onClose={() => setShowFilters(false)}
        />
      )}

      {/* Stats */}
      {data && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">
                {data.pagination.totalCount}
              </div>
              <p className="text-sm text-muted-foreground">Tổng số logs</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">
                {data.pagination.totalPages}
              </div>
              <p className="text-sm text-muted-foreground">Tổng số trang</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{data.pagination.page}</div>
              <p className="text-sm text-muted-foreground">Trang hiện tại</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4">
              <div className="text-2xl font-bold">{data.data.length}</div>
              <p className="text-sm text-muted-foreground">Logs trên trang</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Thời gian</TableHead>
                <TableHead>Admin</TableHead>
                <TableHead>Hành động</TableHead>
                <TableHead>Tài nguyên</TableHead>
                <TableHead>Mô tả</TableHead>
                <TableHead>IP Address</TableHead>
                <TableHead className="text-right">Thao tác</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {data?.data.map((log) => (
                <TableRow key={log.id}>
                  <TableCell className="font-mono text-sm">
                    {format(new Date(log.createdAt), "dd/MM/yyyy HH:mm:ss", {
                      locale: vi,
                    })}
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={log.admin.avatar} />
                        <AvatarFallback>
                          {log.admin.name.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{log.admin.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {log.admin.role}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getActionBadgeVariant(log.action)}>
                      {log.action}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <span>{getResourceIcon(log.resource)}</span>
                      <span>{log.resource}</span>
                      {log.resourceId && (
                        <span className="text-sm text-muted-foreground">
                          #{log.resourceId.slice(-8)}
                        </span>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="max-w-xs truncate">
                    {log.description || "-"}
                  </TableCell>
                  <TableCell className="font-mono text-sm">
                    {log.ipAddress || "-"}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSelectedLog(log)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Pagination */}
      {data && data.pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-muted-foreground">
            Hiển thị {(data.pagination.page - 1) * data.pagination.limit + 1} -{" "}
            {Math.min(
              data.pagination.page * data.pagination.limit,
              data.pagination.totalCount
            )}{" "}
            trong tổng số {data.pagination.totalCount} logs
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(data.pagination.page - 1)}
              disabled={!data.pagination.hasPrevPage}
            >
              <ChevronLeft className="h-4 w-4" />
              Trước
            </Button>
            <span className="text-sm">
              Trang {data.pagination.page} / {data.pagination.totalPages}
            </span>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(data.pagination.page + 1)}
              disabled={!data.pagination.hasNextPage}
            >
              Sau
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Detail Modal */}
      {selectedLog && (
        <AuditLogDetail
          log={selectedLog}
          onClose={() => setSelectedLog(null)}
        />
      )}
    </div>
  );
}
