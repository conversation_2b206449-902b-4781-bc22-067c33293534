import { useState, useCallback } from 'react';
import { toast } from 'sonner';

export interface AuditLogEntry {
  id: string;
  action: string;
  resource: string;
  adminId: string;
  adminName: string;
  adminEmail: string;
  description?: string;
  metadata?: any;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
}

export interface AuditLogFilters {
  page: number;
  limit: number;
  action: string;
  resource: string;
  adminId: string;
  startDate: string;
  endDate: string;
  search: string;
  sortBy: string;
  sortOrder: string;
}

export interface AuditLogFilterOptions {
  actions: string[];
  resources: string[];
  admins: Array<{
    id: string;
    name: string;
    email: string;
  }>;
}

export const useAdminAuditLogs = () => {
  const [auditLogs, setAuditLogs] = useState<AuditLogEntry[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [filterOptions, setFilterOptions] = useState<AuditLogFilterOptions>({
    actions: [],
    resources: [],
    admins: [],
  });

  const fetchAuditLogs = useCallback(async (filters: Partial<AuditLogFilters>) => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/admin/audit-logs?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setAuditLogs(data.auditLogs || []);
        setTotalCount(data.totalCount || 0);
        return { success: true, data };
      } else {
        toast.error("Có lỗi xảy ra khi tải audit logs");
        return { success: false };
      }
    } catch (error) {
      console.error("Failed to fetch audit logs:", error);
      toast.error("Có lỗi xảy ra khi tải audit logs");
      return { success: false };
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchFilterOptions = useCallback(async () => {
    try {
      const response = await fetch("/api/admin/audit-logs", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ type: "filter-options" }),
      });

      if (response.ok) {
        const data = await response.json();
        setFilterOptions(data);
        return { success: true, data };
      } else {
        toast.error("Có lỗi xảy ra khi tải tùy chọn lọc");
        return { success: false };
      }
    } catch (error) {
      console.error("Failed to fetch filter options:", error);
      toast.error("Có lỗi xảy ra khi tải tùy chọn lọc");
      return { success: false };
    }
  }, []);

  const exportAuditLogs = useCallback(async (filters: Partial<AuditLogFilters>) => {
    try {
      const params = new URLSearchParams();
      
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/admin/audit-logs/export?${params.toString()}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `audit-logs-${new Date().toISOString().split("T")[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        toast.success("Đã xuất audit logs thành công");
        return { success: true };
      } else {
        toast.error("Có lỗi xảy ra khi xuất audit logs");
        return { success: false };
      }
    } catch (error) {
      console.error("Failed to export audit logs:", error);
      toast.error("Có lỗi xảy ra khi xuất audit logs");
      return { success: false };
    }
  }, []);

  return {
    auditLogs,
    loading,
    totalCount,
    filterOptions,
    fetchAuditLogs,
    fetchFilterOptions,
    exportAuditLogs,
  };
};