import { useCallback, useEffect } from "react";
import { useAdminState } from "@/contexts/AdminContext";
import { useAdminApi } from "./useAdminApi";
import { 
  ContactResponseDto, 
  ContactStatsDto, 
  ContactFiltersDto,
  ContactStatus,
  ContactPriority,
  CreateContactNoteDto,
  UpdateContactDto
} from "@/app/dto/contact.dto";

export interface AdminContactsFilters {
  search?: string;
  status?: ContactStatus;
  priority?: ContactPriority;
  assignedTo?: string;
  source?: string;
  dateFrom?: string;
  dateTo?: string;
  tags?: string[];
}

export function useAdminContacts() {
  const {
    data: contacts,
    loading,
    error,
    pagination,
    filters,
    updateData,
    updatePagination,
    updateFilters,
    setLoading,
    setError,
    refresh,
  } = useAdminState("admin-contacts");

  const { apiCall, isOperationLoading } = useAdminApi();

  const fetchContacts = useCallback(
    async (
      page: number = 1,
      limit: number = 20,
      searchFilters: AdminContactsFilters = {}
    ) => {
      setLoading(true);
      setError(null);

      try {
        const params = new URLSearchParams();
        params.append("page", page.toString());
        params.append("limit", limit.toString());

        // Add filters with proper string conversion
        Object.entries(searchFilters).forEach(([key, value]) => {
          if (value !== undefined && value !== "" && value !== null) {
            if (Array.isArray(value)) {
              value.forEach(v => params.append(key, v.toString()));
            } else {
              params.append(key, value.toString());
            }
          }
        });

        const response = await apiCall<{ contacts: ContactResponseDto[], pagination: any }>(
          `/api/admin/contacts?${params}`,
          { method: "GET" }
        );

        if (response.success && response.data) {
          updateData(response.data.contacts);
          if (response.data.pagination) {
            updatePagination(response.data.pagination);
          }
          updateFilters(searchFilters);
        }
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to fetch contacts"
        );
      }
    },
    [apiCall, updateData, updatePagination, updateFilters, setLoading, setError]
  );

  const getContactById = useCallback(
    async (id: string): Promise<ContactResponseDto | null> => {
      try {
        const response = await apiCall<ContactResponseDto>(
          `/api/admin/contacts/${id}`,
          { method: "GET" },
          `get-contact-${id}`
        );

        return response.success && response.data ? response.data : null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to get contact"
        );
      }
    },
    [apiCall]
  );

  const updateContact = useCallback(
    async (
      id: string,
      updateData: UpdateContactDto
    ): Promise<ContactResponseDto | null> => {
      try {
        const response = await apiCall<ContactResponseDto>(
          `/api/admin/contacts/${id}`,
          {
            method: "PUT",
            body: updateData,
          },
          `update-contact-${id}`
        );

        if (response.success && response.data) {
          const updatedContacts = contacts.map((contact) =>
            contact.id === id ? response.data! : contact
          );
          updateData(updatedContacts);
          return response.data;
        }
        return null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to update contact"
        );
      }
    },
    [apiCall, contacts, updateData]
  );

  const updateContactStatus = useCallback(
    async (
      id: string,
      status: ContactStatus
    ): Promise<ContactResponseDto | null> => {
      return updateContact(id, { status });
    },
    [updateContact]
  );

  const assignContact = useCallback(
    async (
      id: string,
      assignedTo: string | null
    ): Promise<ContactResponseDto | null> => {
      return updateContact(id, { assignedTo });
    },
    [updateContact]
  );

  const addContactNote = useCallback(
    async (id: string, noteData: CreateContactNoteDto): Promise<boolean> => {
      try {
        const response = await apiCall(
          `/api/admin/contacts/${id}/notes`,
          {
            method: "POST",
            body: noteData,
          },
          `add-contact-note-${id}`
        );

        if (response.success) {
          // Refresh the contact to get updated notes
          const updatedContact = await getContactById(id);
          if (updatedContact) {
            const updatedContacts = contacts.map((contact) =>
              contact.id === id ? updatedContact : contact
            );
            updateData(updatedContacts);
          }
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to add note"
        );
      }
    },
    [apiCall, getContactById, contacts, updateData]
  );

  const deleteContact = useCallback(
    async (id: string): Promise<boolean> => {
      try {
        const response = await apiCall(
          `/api/admin/contacts/${id}`,
          { method: "DELETE" },
          `delete-contact-${id}`
        );

        if (response.success) {
          const filteredContacts = contacts.filter(
            (contact) => contact.id !== id
          );
          updateData(filteredContacts);
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to delete contact"
        );
      }
    },
    [apiCall, contacts, updateData]
  );

  const getContactStats =
    useCallback(async (): Promise<ContactStatsDto | null> => {
      try {
        const response = await apiCall<ContactStatsDto>(
          "/api/admin/contacts/stats",
          { method: "GET" },
          "get-contact-stats"
        );

        return response.success && response.data ? response.data : null;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to get contact stats"
        );
      }
    }, [apiCall]);

  const sendTestEmail = useCallback(
    async (to: string, template: string): Promise<boolean> => {
      try {
        const response = await apiCall(
          "/api/admin/contacts/test-email",
          {
            method: "POST",
            body: { to, template },
          },
          "send-test-email"
        );

        return response.success;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to send test email"
        );
      }
    },
    [apiCall]
  );

  const bulkUpdateContacts = useCallback(
    async (ids: string[], updateData: UpdateContactDto): Promise<boolean> => {
      try {
        const response = await apiCall(
          "/api/admin/contacts/bulk",
          {
            method: "PUT",
            body: { ids, data: updateData },
          },
          "bulk-update-contacts"
        );

        if (response.success) {
          // Refresh contacts to get updated data
          await fetchContacts(pagination.page, pagination.limit, filters as AdminContactsFilters);
          return true;
        }
        return false;
      } catch (err) {
        throw new Error(
          err instanceof Error ? err.message : "Failed to bulk update contacts"
        );
      }
    },
    [apiCall, fetchContacts, pagination, filters]
  );

  const searchContacts = useCallback(
    (searchFilters: AdminContactsFilters) => {
      fetchContacts(1, pagination.limit, searchFilters);
    },
    [fetchContacts, pagination.limit]
  );

  const changePage = useCallback(
    (page: number) => {
      fetchContacts(page, pagination.limit, filters as AdminContactsFilters);
    },
    [fetchContacts, pagination.limit, filters]
  );

  const changePageSize = useCallback(
    (limit: number) => {
      fetchContacts(1, limit, filters as AdminContactsFilters);
    },
    [fetchContacts, filters]
  );

  // Auto-fetch on mount if no data
  useEffect(() => {
    if (contacts.length === 0 && !loading && !error) {
      fetchContacts();
    }
  }, [contacts.length, loading, error, fetchContacts]);

  return {
    // Data
    contacts,
    loading,
    error,
    pagination,
    filters,

    // Actions
    fetchContacts,
    getContactById,
    updateContact,
    updateContactStatus,
    assignContact,
    addContactNote,
    deleteContact,
    getContactStats,
    sendTestEmail,
    bulkUpdateContacts,
    searchContacts,
    changePage,
    changePageSize,
    refresh,

    // Loading states
    isGettingContact: (id: string) => isOperationLoading(`get-contact-${id}`),
    isUpdatingContact: (id: string) => isOperationLoading(`update-contact-${id}`),
    isAddingNote: (id: string) => isOperationLoading(`add-contact-note-${id}`),
    isDeleting: (id: string) => isOperationLoading(`delete-contact-${id}`),
    isGettingStats: isOperationLoading("get-contact-stats"),
    isSendingTestEmail: isOperationLoading("send-test-email"),
    isBulkUpdating: isOperationLoading("bulk-update-contacts"),
  };
}