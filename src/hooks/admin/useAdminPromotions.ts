/**
 * Admin Promotions Hook
 * Hook để quản lý promotions trong admin panel
 */

import { useState, useCallback } from 'react';
import { useAdminApi } from './useAdminApi';
import { 
  PromotionResponseDto, 
  PromotionListResponseDto,
  CreatePromotionDto,
  UpdatePromotionDto,
  PromotionFiltersDto,
  PromotionStatsDto,
  BulkPromotionOperationDto,
  ApplyPromotionDto,
  PromotionApplicationResultDto
} from '@/app/dto/promotion.dto';
import { PromotionType } from '@prisma/client';

/**
 * Admin Promotions Filters
 */
export interface AdminPromotionsFilters {
  page?: number;
  limit?: number;
  search?: string;
  type?: PromotionType;
  isActive?: boolean;
  status?: 'DRAFT' | 'ACTIVE' | 'PAUSED' | 'EXPIRED' | 'CANCELLED';
  startDate?: string;
  endDate?: string;
  createdBy?: string;
  sortBy?: 'name' | 'code' | 'type' | 'value' | 'startDate' | 'endDate' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Promotion Form Data
 */
export interface PromotionFormData {
  name: string;
  description?: string;
  code: string;
  type: PromotionType;
  value: number;
  minOrderAmount?: number;
  maxDiscountAmount?: number;
  usageLimit?: number;
  userUsageLimit?: number;
  startDate: string;
  endDate?: string;
  isActive: boolean;
  applicableProducts: string[];
  applicableCategories: string[];
  excludedProducts: string[];
  excludedCategories: string[];
}

/**
 * Hook return type
 */
export interface UseAdminPromotionsReturn {
  // Data
  promotions: PromotionResponseDto[] | null;
  promotion: PromotionResponseDto | null;
  stats: PromotionStatsDto | null;
  
  // Loading states
  loading: boolean;
  creating: boolean;
  updating: boolean;
  deleting: boolean;
  
  // Error states
  error: string | null;
  
  // Actions
  fetchPromotions: (filters?: AdminPromotionsFilters) => Promise<PromotionListResponseDto | null>;
  fetchPromotion: (id: string) => Promise<PromotionResponseDto | null>;
  createPromotion: (data: PromotionFormData) => Promise<PromotionResponseDto | null>;
  updatePromotion: (id: string, data: Partial<PromotionFormData>) => Promise<PromotionResponseDto | null>;
  deletePromotion: (id: string) => Promise<boolean>;
  bulkOperation: (data: BulkPromotionOperationDto) => Promise<boolean>;
  duplicatePromotion: (id: string) => Promise<PromotionResponseDto | null>;
  togglePromotionStatus: (id: string) => Promise<PromotionResponseDto | null>;
  testPromotion: (data: ApplyPromotionDto) => Promise<PromotionApplicationResultDto | null>;
  fetchStats: () => Promise<PromotionStatsDto | null>;
  
  // Pagination
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  } | null;
  
  // Utilities
  clearError: () => void;
  reset: () => void;
}

/**
 * Admin Promotions Hook
 */
export function useAdminPromotions(): UseAdminPromotionsReturn {
  const { apiCall } = useAdminApi();
  
  // State
  const [promotions, setPromotions] = useState<PromotionResponseDto[] | null>(null);
  const [promotion, setPromotion] = useState<PromotionResponseDto | null>(null);
  const [stats, setStats] = useState<PromotionStatsDto | null>(null);
  const [pagination, setPagination] = useState<{
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  } | null>(null);
  
  // Loading states
  const [loading, setLoading] = useState(false);
  const [creating, setCreating] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [deleting, setDeleting] = useState(false);
  
  // Error state
  const [error, setError] = useState<string | null>(null);

  /**
   * Fetch promotions with filters
   */
  const fetchPromotions = useCallback(async (filters?: AdminPromotionsFilters) => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams();
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null && value !== '') {
            params.append(key, value.toString());
          }
        });
      }
      
      const response = await apiCall<PromotionListResponseDto>({
        method: 'GET',
        endpoint: `/admin/promotions?${params.toString()}`,
      });
      
      if (response.success && response.data) {
        setPromotions(response.data.data);
        setPagination(response.data.pagination);
        return response.data;
      }
      
      throw new Error(response.error || 'Failed to fetch promotions');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch promotions';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  /**
   * Fetch single promotion
   */
  const fetchPromotion = useCallback(async (id: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiCall<PromotionResponseDto>({
        method: 'GET',
        endpoint: `/admin/promotions/${id}`,
      });
      
      if (response.success && response.data) {
        setPromotion(response.data);
        return response.data;
      }
      
      throw new Error(response.error || 'Failed to fetch promotion');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch promotion';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  /**
   * Create new promotion
   */
  const createPromotion = useCallback(async (data: PromotionFormData) => {
    try {
      setCreating(true);
      setError(null);
      
      const response = await apiCall<PromotionResponseDto>({
        method: 'POST',
        endpoint: '/admin/promotions',
        data,
      });
      
      if (response.success && response.data) {
        // Refresh promotions list
        await fetchPromotions();
        return response.data;
      }
      
      throw new Error(response.error || 'Failed to create promotion');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create promotion';
      setError(errorMessage);
      return null;
    } finally {
      setCreating(false);
    }
  }, [apiCall, fetchPromotions]);

  /**
   * Update promotion
   */
  const updatePromotion = useCallback(async (id: string, data: Partial<PromotionFormData>) => {
    try {
      setUpdating(true);
      setError(null);
      
      const response = await apiCall<PromotionResponseDto>({
        method: 'PUT',
        endpoint: `/admin/promotions/${id}`,
        data,
      });
      
      if (response.success && response.data) {
        // Update local state
        if (promotion?.id === id) {
          setPromotion(response.data);
        }
        
        // Refresh promotions list
        await fetchPromotions();
        return response.data;
      }
      
      throw new Error(response.error || 'Failed to update promotion');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update promotion';
      setError(errorMessage);
      return null;
    } finally {
      setUpdating(false);
    }
  }, [request, promotion, fetchPromotions]);

  /**
   * Delete promotion
   */
  const deletePromotion = useCallback(async (id: string) => {
    try {
      setDeleting(true);
      setError(null);
      
      const response = await apiCall({
        method: 'DELETE',
        endpoint: `/admin/promotions/${id}`,
      });
      
      if (response.success) {
        // Remove from local state
        if (promotions) {
          setPromotions(promotions.filter(p => p.id !== id));
        }
        
        if (promotion?.id === id) {
          setPromotion(null);
        }
        
        return true;
      }
      
      throw new Error(response.error || 'Failed to delete promotion');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete promotion';
      setError(errorMessage);
      return false;
    } finally {
      setDeleting(false);
    }
  }, [request, promotions, promotion]);

  /**
   * Bulk operations
   */
  const bulkOperation = useCallback(async (data: BulkPromotionOperationDto) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiCall({
        method: 'POST',
        endpoint: '/admin/promotions/bulk',
        data,
      });
      
      if (response.success) {
        // Refresh promotions list
        await fetchPromotions();
        return true;
      }
      
      throw new Error(response.error || 'Failed to perform bulk operation');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to perform bulk operation';
      setError(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, [apiCall, fetchPromotions]);

  /**
   * Duplicate promotion
   */
  const duplicatePromotion = useCallback(async (id: string) => {
    try {
      setCreating(true);
      setError(null);
      
      const response = await apiCall<PromotionResponseDto>({
        method: 'POST',
        endpoint: `/admin/promotions/${id}/duplicate`,
      });
      
      if (response.success && response.data) {
        // Refresh promotions list
        await fetchPromotions();
        return response.data;
      }
      
      throw new Error(response.error || 'Failed to duplicate promotion');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to duplicate promotion';
      setError(errorMessage);
      return null;
    } finally {
      setCreating(false);
    }
  }, [apiCall, fetchPromotions]);

  /**
   * Toggle promotion status
   */
  const togglePromotionStatus = useCallback(async (id: string) => {
    try {
      setUpdating(true);
      setError(null);
      
      const response = await apiCall<PromotionResponseDto>({
        method: 'PATCH',
        endpoint: `/admin/promotions/${id}/toggle-status`,
      });
      
      if (response.success && response.data) {
        // Update local state
        if (promotion?.id === id) {
          setPromotion(response.data);
        }
        
        // Refresh promotions list
        await fetchPromotions();
        return response.data;
      }
      
      throw new Error(response.error || 'Failed to toggle promotion status');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to toggle promotion status';
      setError(errorMessage);
      return null;
    } finally {
      setUpdating(false);
    }
  }, [request, promotion, fetchPromotions]);

  /**
   * Test promotion
   */
  const testPromotion = useCallback(async (data: ApplyPromotionDto) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiCall<PromotionApplicationResultDto>({
        method: 'POST',
        endpoint: '/admin/promotions/test',
        data,
      });
      
      if (response.success && response.data) {
        return response.data;
      }
      
      throw new Error(response.error || 'Failed to test promotion');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to test promotion';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  /**
   * Fetch promotion stats
   */
  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiCall<PromotionStatsDto>({
        method: 'GET',
        endpoint: '/admin/promotions/stats',
      });
      
      if (response.success && response.data) {
        setStats(response.data);
        return response.data;
      }
      
      throw new Error(response.error || 'Failed to fetch promotion stats');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch promotion stats';
      setError(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, [apiCall]);

  /**
   * Clear error
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  /**
   * Reset state
   */
  const reset = useCallback(() => {
    setPromotions(null);
    setPromotion(null);
    setStats(null);
    setPagination(null);
    setError(null);
  }, []);

  return {
    // Data
    promotions,
    promotion,
    stats,
    
    // Loading states
    loading,
    creating,
    updating,
    deleting,
    
    // Error state
    error,
    
    // Actions
    fetchPromotions,
    fetchPromotion,
    createPromotion,
    updatePromotion,
    deletePromotion,
    bulkOperation,
    duplicatePromotion,
    togglePromotionStatus,
    testPromotion,
    fetchStats,
    
    // Pagination
    pagination,
    
    // Utilities
    clearError,
    reset,
  };
}