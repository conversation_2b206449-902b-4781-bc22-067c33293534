/**
 * Admin Hooks Index
 * Export all admin-related hooks
 */

// Core API hook
export { useAdminApi } from './useAdminApi';

// Entity-specific hooks
export { useAdminUsers } from './useAdminUsers';
export { useAdminProducts } from './useAdminProducts';
export { useAdminCategories } from './useAdminCategories';
export { useAdminOrders } from './useAdminOrders';
export { useAdminBrands } from './useAdminBrands';
export { useAdminContacts } from './useAdminContacts';
export { useAdminMedia } from './useAdminMedia';
export { useAdminNotifications } from './useAdminNotifications';
export { useAdminSettings } from './useAdminSettings';
export { useAdminPosts } from './useAdminPosts';
export { useAdminEmailTemplates } from './useAdminEmailTemplates';
export { useAdminAttributes } from './useAdminAttributes';
export { useAdminPromotions } from './useAdminPromotions';
export { useAdminLogs } from './useAdminLogs';
export { useAdminAuditLogs } from './useAdminAuditLogs';
export { useAdminInventory } from './useAdminInventory';
export { useAdminSEO } from './useAdminSEO';
export { useAdminPages } from './useAdminPages';

// Dashboard and Analytics hooks
export { useAdminDashboard } from './useAdminDashboard';
export { useAdminAnalytics } from './useAdminAnalytics';

// Type exports for filters
export type { AdminUsersFilters } from './useAdminUsers';
export type { AdminProductsFilters } from './useAdminProducts';
export type { AdminCategoriesFilters } from './useAdminCategories';
export type { AdminOrdersFilters } from './useAdminOrders';
export type { AdminBrandsFilters } from './useAdminBrands';
export type { AdminContactsFilters } from './useAdminContacts';
export type { AdminMediaFilters } from './useAdminMedia';
export type { AdminNotificationsFilters } from './useAdminNotifications';

// Type exports for DTOs
export type { AdminContactsFilters } from './useAdminContacts';
export type { MediaDto } from './useAdminMedia';
export type { NotificationDto, NotificationPreferencesDto } from './useAdminNotifications';
export type { DashboardStatsDto, DashboardOverviewDto, DashboardAlertDto, PerformanceMetricsDto } from './useAdminDashboard';
export type { AnalyticsDataDto, AnalyticsFilters } from './useAdminAnalytics';
export type { Post, PostFilters, PostFormData } from './useAdminPosts';
export type { EmailTemplate, EmailStatus } from './useAdminEmailTemplates';
export type { AttributeFormData } from './useAdminAttributes';
export type { LogEntry, LogFilters } from './useAdminLogs';
export type { AuditLogEntry, AuditLogFilters, AuditLogFilterOptions } from './useAdminAuditLogs';
export type {
  InventoryItem,
  InventoryStats,
  InventoryFilters,
  InventoryMovement,
  MovementFilters,
  CreateInventoryData,
} from '@/types/inventory';
export type { GlobalSEOSettings } from './useAdminSEO';
export type { Page, PageFormData } from './useAdminPages';