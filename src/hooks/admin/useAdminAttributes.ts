import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import { Attribute, AttributeFilters, AttributeFormData as AttributeFormDataType } from '@/types/attribute';

export type AttributeFormData = AttributeFormDataType;

export const useAdminAttributes = () => {
  const [attributes, setAttributes] = useState<Attribute[]>([]);
  const [loading, setLoading] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [saving, setSaving] = useState(false);

  const fetchAttributes = useCallback(async (filters: AttributeFilters = {}) => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value && value !== "all") params.append(key, value.toString());
      });

      const response = await fetch(`/api/admin/attributes?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setAttributes(data.attributes || []);
      } else {
        toast.error("Có lỗi xảy ra khi tải danh sách thuộc tính");
      }
    } catch {
      toast.error("Có lỗi xảy ra khi tải danh sách thuộc tính");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchAttribute = useCallback(async (id: string) => {
    try {
      const response = await fetch(`/api/admin/attributes/${id}`);
      if (response.ok) {
        const data = await response.json();
        return { success: true, data };
      } else {
        const error = await response.json();
        toast.error(error.error || "Có lỗi xảy ra khi tải thuộc tính");
        return { success: false, error: error.error };
      }
    } catch {
      toast.error("Có lỗi xảy ra khi tải thuộc tính");
      return { success: false, error: "Network error" };
    }
  }, []);

  const createAttribute = useCallback(async (attributeData: AttributeFormData) => {
    setSaving(true);
    try {
      const response = await fetch('/api/admin/attributes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(attributeData),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Thuộc tính đã được tạo thành công');
        return { success: true, data };
      } else {
        toast.error(data.error || 'Có lỗi xảy ra khi tạo thuộc tính');
        return { success: false, error: data.error };
      }
    } catch {
      toast.error('Có lỗi xảy ra khi tạo thuộc tính');
      return { success: false, error: 'Network error' };
    } finally {
      setSaving(false);
    }
  }, []);

  const updateAttribute = useCallback(async (id: string, attributeData: AttributeFormData) => {
    setSaving(true);
    try {
      const response = await fetch(`/api/admin/attributes/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(attributeData),
      });

      const data = await response.json();

      if (response.ok) {
        toast.success("Thuộc tính đã được cập nhật thành công");
        return { success: true, data };
      } else {
        toast.error(data.error || "Có lỗi xảy ra khi cập nhật thuộc tính");
        return { success: false, error: data.error };
      }
    } catch {
      toast.error("Có lỗi xảy ra khi cập nhật thuộc tính");
      return { success: false, error: "Network error" };
    } finally {
      setSaving(false);
    }
  }, []);

  const deleteAttribute = useCallback(async (id: string) => {
    setDeleting(true);
    try {
      const response = await fetch(`/api/admin/attributes/${id}`, {
        method: "DELETE",
      });

      if (response.ok) {
        toast.success("Thuộc tính đã được xóa thành công");
        return { success: true };
      } else {
        const data = await response.json();
        toast.error(data.error || "Có lỗi xảy ra khi xóa thuộc tính");
        return { success: false, error: data.error };
      }
    } catch {
      toast.error("Có lỗi xảy ra khi xóa thuộc tính");
      return { success: false, error: "Network error" };
    } finally {
      setDeleting(false);
    }
  }, []);

  return {
    attributes,
    loading,
    deleting,
    saving,
    fetchAttributes,
    fetchAttribute,
    createAttribute,
    updateAttribute,
    deleteAttribute,
  };
};