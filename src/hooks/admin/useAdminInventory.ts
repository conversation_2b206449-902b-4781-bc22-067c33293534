import { useState, useCallback } from 'react';
import { toast } from 'sonner';
import {
  InventoryItem,
  InventoryStats,
  InventoryFilters,
  InventoryMovement,
  MovementFilters,
  CreateInventoryData,
  InventoryPagination,
} from '@/types/inventory';


export const useAdminInventory = () => {
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [movements, setMovements] = useState<InventoryMovement[]>([]);
  const [stats, setStats] = useState<InventoryStats | null>(null);
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [statsLoading, setStatsLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);

  const fetchStats = useCallback(async () => {
    setStatsLoading(true);
    try {
      const response = await fetch("/api/admin/inventory/stats");
      const data = await response.json();

      if (response.ok) {
        setStats(data);
      } else {
        toast.error("Có lỗi xảy ra khi tải thống kê kho");
      }
    } catch {
      toast.error("Có lỗi xảy ra khi tải thống kê kho");
    } finally {
      setStatsLoading(false);
    }
  }, []);

  const fetchInventory = useCallback(async (
    filters: InventoryFilters = {},
    pagination: InventoryPagination = { page: 1, limit: 20 }
  ) => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
      });

      if (filters.search) params.append("search", filters.search);
      if (filters.status && filters.status !== "all") {
        params.append("status", filters.status);
      }
      if (filters.location) params.append("location", filters.location);

      const response = await fetch(`/api/admin/inventory?${params.toString()}`);
      const data = await response.json();

      if (response.ok) {
        setInventory(data.inventory || []);
        setTotalCount(data.totalCount || 0);
      } else {
        toast.error("Có lỗi xảy ra khi tải danh sách kho");
      }
    } catch {
      toast.error("Có lỗi xảy ra khi tải danh sách kho");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchMovements = useCallback(async (
    filters: MovementFilters = {},
    pagination: InventoryPagination = { page: 1, limit: 20 }
  ) => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      
      if (filters.search) params.append("search", filters.search);
      if (filters.type && filters.type !== "all") {
        params.append("type", filters.type);
      }
      if (filters.startDate) params.append("startDate", filters.startDate);
      if (filters.endDate) params.append("endDate", filters.endDate);
      params.append("page", pagination.page.toString());
      params.append("limit", pagination.limit.toString());

      const response = await fetch(
        `/api/admin/inventory/movements?${params.toString()}`
      );
      const data = await response.json();

      if (response.ok) {
        setMovements(data.movements || []);
        setTotalCount(data.totalCount || 0);
      } else {
        toast.error("Có lỗi xảy ra khi tải lịch sử biến động kho");
      }
    } catch {
      toast.error("Có lỗi xảy ra khi tải lịch sử biến động kho");
    } finally {
      setLoading(false);
    }
  }, []);

  const fetchProducts = useCallback(async () => {
    try {
      const response = await fetch('/api/admin/products');
      if (response.ok) {
        const data = await response.json();
        setProducts(data.products || []);
      } else {
        toast.error("Có lỗi xảy ra khi tải danh sách sản phẩm");
      }
    } catch {
      toast.error("Có lỗi xảy ra khi tải danh sách sản phẩm");
    }
  }, []);

  const createInventoryEntry = useCallback(async (data: CreateInventoryData) => {
    try {
      const response = await fetch('/api/admin/inventory', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success('Đã tạo phiếu kho thành công');
        return { success: true, data: result };
      } else {
        toast.error(result.error || 'Có lỗi xảy ra khi tạo phiếu kho');
        return { success: false, error: result.error };
      }
    } catch {
      toast.error('Có lỗi xảy ra khi tạo phiếu kho');
      return { success: false, error: 'Network error' };
    }
  }, []);

  return {
    inventory,
    movements,
    stats,
    products,
    loading,
    statsLoading,
    totalCount,
    fetchStats,
    fetchInventory,
    fetchMovements,
    fetchProducts,
    createInventoryEntry,
  };
};