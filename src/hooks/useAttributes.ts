import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";
import {
  Attribute,
  AttributeFilters,
  AttributeFormData,
  AttributeStats,
} from "../types/attribute";

interface UseAttributesOptions {
  initialFilters?: AttributeFilters;
  autoFetch?: boolean;
}

interface UseAttributesReturn {
  attributes: Attribute[];
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  filters: AttributeFilters;
  stats: AttributeStats | null;
  
  // Actions
  fetchAttributes: () => Promise<void>;
  createAttribute: (data: AttributeFormData) => Promise<Attribute | null>;
  updateAttribute: (id: string, data: Partial<AttributeFormData>) => Promise<Attribute | null>;
  deleteAttribute: (id: string) => Promise<boolean>;
  bulkDelete: (ids: string[]) => Promise<boolean>;
  bulkUpdate: (ids: string[], data: Partial<AttributeFormData>) => Promise<boolean>;
  setFilters: (filters: Partial<AttributeFilters>) => void;
  resetFilters: () => void;
  fetchStats: () => Promise<void>;
  refresh: () => Promise<void>;
}

const defaultFilters: AttributeFilters = {
  page: 1,
  limit: 20,
  sortBy: "sortOrder",
  sortOrder: "asc",
};

export function useAttributes(options: UseAttributesOptions = {}): UseAttributesReturn {
  const { initialFilters = {}, autoFetch = true } = options;

  const [attributes, setAttributes] = useState<Attribute[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0,
  });
  const [filters, setFiltersState] = useState<AttributeFilters>({
    ...defaultFilters,
    ...initialFilters,
  });
  const [stats, setStats] = useState<AttributeStats | null>(null);

  const fetchAttributes = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== "") {
          params.append(key, value.toString());
        }
      });

      const response = await fetch(`/api/admin/attributes?${params}`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch attributes");
      }

      const data = await response.json();
      setAttributes(data.data || []);
      setPagination(data.pagination || pagination);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Có lỗi xảy ra khi tải danh sách thuộc tính";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [filters]);

  const createAttribute = useCallback(async (data: AttributeFormData): Promise<Attribute | null> => {
    try {
      const response = await fetch("/api/admin/attributes", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Có lỗi xảy ra khi tạo thuộc tính");
      }

      const newAttribute = await response.json();
      setAttributes(prev => [newAttribute, ...prev]);
      toast.success("Tạo thuộc tính thành công");
      return newAttribute;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Có lỗi xảy ra khi tạo thuộc tính";
      setError(errorMessage);
      toast.error(errorMessage);
      return null;
    }
  }, []);

  const updateAttribute = useCallback(async (id: string, data: Partial<AttributeFormData>): Promise<Attribute | null> => {
    try {
      const response = await fetch(`/api/admin/attributes/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Có lỗi xảy ra khi cập nhật thuộc tính");
      }

      const updatedAttribute = await response.json();
      setAttributes(prev => 
        prev.map(attr => attr.id === id ? updatedAttribute : attr)
      );
      toast.success("Cập nhật thuộc tính thành công");
      return updatedAttribute;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Có lỗi xảy ra khi cập nhật thuộc tính";
      setError(errorMessage);
      toast.error(errorMessage);
      return null;
    }
  }, []);

  const deleteAttribute = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await fetch(`/api/admin/attributes/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Có lỗi xảy ra khi xóa thuộc tính");
      }

      setAttributes(prev => prev.filter(attr => attr.id !== id));
      toast.success("Xóa thuộc tính thành công");
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Có lỗi xảy ra khi xóa thuộc tính";
      setError(errorMessage);
      toast.error(errorMessage);
      return false;
    }
  }, []);

  const bulkDelete = useCallback(async (ids: string[]): Promise<boolean> => {
    try {
      const response = await fetch("/api/admin/attributes/bulk", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "delete",
          ids,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Có lỗi xảy ra khi xóa thuộc tính");
      }

      setAttributes(prev => prev.filter(attr => !ids.includes(attr.id)));
      const result = await response.json();
      toast.success(result.message || "Xóa thuộc tính thành công");
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Có lỗi xảy ra khi xóa thuộc tính";
      setError(errorMessage);
      toast.error(errorMessage);
      return false;
    }
  }, []);

  const bulkUpdate = useCallback(async (ids: string[], data: Partial<AttributeFormData>): Promise<boolean> => {
    try {
      const response = await fetch("/api/admin/attributes/bulk", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action: "update",
          ids,
          data,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Có lỗi xảy ra khi cập nhật thuộc tính");
      }

      // Refresh attributes to get updated data
      await fetchAttributes();
      const result = await response.json();
      toast.success(result.message || "Cập nhật thuộc tính thành công");
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Có lỗi xảy ra khi cập nhật thuộc tính";
      setError(errorMessage);
      toast.error(errorMessage);
      return false;
    }
  }, [fetchAttributes]);

  const setFilters = useCallback((newFilters: Partial<AttributeFilters>) => {
    setFiltersState(prev => ({
      ...prev,
      ...newFilters,
      page: newFilters.page || 1, // Reset to page 1 when filters change (except when explicitly setting page)
    }));
  }, []);

  const resetFilters = useCallback(() => {
    setFiltersState(defaultFilters);
  }, []);

  const fetchStats = useCallback(async () => {
    try {
      const response = await fetch("/api/admin/attributes/stats");
      
      if (!response.ok) {
        throw new Error("Failed to fetch stats");
      }

      const statsData = await response.json();
      setStats(statsData);
    } catch (err) {
      console.error("Error fetching attribute stats:", err);
    }
  }, []);

  const refresh = useCallback(async () => {
    await Promise.all([fetchAttributes(), fetchStats()]);
  }, [fetchAttributes, fetchStats]);

  // Auto-fetch on mount and when filters change
  useEffect(() => {
    if (autoFetch) {
      fetchAttributes();
    }
  }, [fetchAttributes, autoFetch]);

  // Fetch stats on mount
  useEffect(() => {
    if (autoFetch) {
      fetchStats();
    }
  }, [fetchStats, autoFetch]);

  return {
    attributes,
    loading,
    error,
    pagination,
    filters,
    stats,
    fetchAttributes,
    createAttribute,
    updateAttribute,
    deleteAttribute,
    bulkDelete,
    bulkUpdate,
    setFilters,
    resetFilters,
    fetchStats,
    refresh,
  };
}
