import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";

// Import from central types to avoid duplication
import type { Brand } from "../types";

export interface BrandsFilters {
  search?: string;
  category?: string;
  isPartner?: boolean;
  isActive?: boolean;
  country?: string;
}

export interface UseBrandsOptions {
  initialFilters?: BrandsFilters;
  limit?: number;
}

export function useBrands(options: UseBrandsOptions = {}) {
  const [brands, setBrands] = useState<Brand[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<BrandsFilters>(options.initialFilters || {});

  const fetchBrands = useCallback(async (customFilters?: BrandsFilters) => {
    setLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams();
      const finalFilters = { ...filters, ...customFilters };
      
      if (finalFilters.search) params.append("search", finalFilters.search);
      if (finalFilters.category) params.append("category", finalFilters.category);
      if (finalFilters.isPartner !== undefined) params.append("isPartner", finalFilters.isPartner.toString());
      if (finalFilters.isActive !== undefined) params.append("isActive", finalFilters.isActive.toString());
      if (finalFilters.country) params.append("country", finalFilters.country);
      if (options.limit) params.append("limit", options.limit.toString());

      // Since there's no public brands API yet, we'll use mock data for now
      // In the future, this would be: const response = await fetch(`/api/brands?${params}`);
      
      // Mock data for now - this should be replaced with actual API call
      const mockBrands: Brand[] = [
        {
          id: "1",
          name: "Nike",
          slug: "nike",
          logo: {
            id: "logo1",
            url: "/images/brands/nike.png",
            alt: "Nike logo",
          },
          description: "Thương hiệu thể thao hàng đầu thế giới",
          productCount: 156,
          rating: 4.8,
          isPartner: true,
          category: "Thể thao",
          established: 1971,
          country: "USA",
          website: "https://nike.com",
          isActive: true,
          status: "ACTIVE",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: "2",
          name: "Adidas",
          slug: "adidas",
          logo: {
            id: "logo2",
            url: "/images/brands/adidas.png",
            alt: "Adidas logo",
          },
          description: "Thương hiệu thể thao nổi tiếng từ Đức",
          productCount: 134,
          rating: 4.7,
          isPartner: true,
          category: "Thể thao",
          established: 1949,
          country: "Germany",
          website: "https://adidas.com",
          isActive: true,
          status: "ACTIVE",
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: "3",
          name: "Zara",
          slug: "zara",
          logo: {
            id: "logo3",
            url: "/images/brands/zara.png",
            alt: "Zara logo",
          },
          description: "Thời trang nhanh từ Tây Ban Nha",
          productCount: 289,
          rating: 4.6,
          isPartner: true,
          category: "Thời trang",
          established: 1975,
          country: "Spain",
          website: "https://zara.com",
          isActive: true,
          status: "ACTIVE" as const,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: "4",
          name: "H&M",
          slug: "hm",
          logo: {
            id: "logo4",
            url: "/images/brands/hm.png",
            alt: "H&M logo",
          },
          description: "Thời trang bền vững và phong cách",
          productCount: 245,
          rating: 4.5,
          isPartner: true,
          category: "Thời trang",
          established: 1947,
          country: "Sweden",
          website: "https://hm.com",
          isActive: true,
          status: "ACTIVE" as const,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: "5",
          name: "Uniqlo",
          slug: "uniqlo",
          logo: {
            id: "logo5",
            url: "/images/brands/uniqlo.png",
            alt: "Uniqlo logo",
          },
          description: "Thời trang tối giản từ Nhật Bản",
          productCount: 178,
          rating: 4.7,
          isPartner: true,
          category: "Thời trang",
          established: 1984,
          country: "Japan",
          website: "https://uniqlo.com",
          isActive: true,
          status: "ACTIVE" as const,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: "6",
          name: "Louis Vuitton",
          slug: "louis-vuitton",
          logo: {
            id: "logo6",
            url: "/images/brands/lv.png",
            alt: "Louis Vuitton logo",
          },
          description: "Thương hiệu xa xỉ hàng đầu",
          productCount: 89,
          rating: 4.9,
          isPartner: false,
          category: "Xa xỉ",
          established: 1854,
          country: "France",
          website: "https://louisvuitton.com",
          isActive: true,
          status: "ACTIVE" as const,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ];

      // Apply client-side filtering to mock data
      let filteredBrands = mockBrands;
      
      if (finalFilters.search) {
        const searchLower = finalFilters.search.toLowerCase();
        filteredBrands = filteredBrands.filter(brand => 
          brand.name.toLowerCase().includes(searchLower) ||
          brand.description?.toLowerCase().includes(searchLower)
        );
      }
      
      if (finalFilters.category) {
        filteredBrands = filteredBrands.filter(brand => 
          brand.category.toLowerCase() === finalFilters.category?.toLowerCase()
        );
      }
      
      if (finalFilters.isPartner !== undefined) {
        filteredBrands = filteredBrands.filter(brand => brand.isPartner === finalFilters.isPartner);
      }
      
      if (finalFilters.country) {
        filteredBrands = filteredBrands.filter(brand => 
          brand.country?.toLowerCase() === finalFilters.country?.toLowerCase()
        );
      }

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 500));
      
      setBrands(filteredBrands);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Có lỗi xảy ra khi tải thương hiệu";
      setError(errorMessage);
      console.error("Fetch brands error:", error);
    } finally {
      setLoading(false);
    }
  }, [filters, options.limit]);

  const updateFilters = useCallback((newFilters: Partial<BrandsFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters({});
  }, []);

  useEffect(() => {
    fetchBrands();
  }, [fetchBrands]);

  return {
    brands,
    loading,
    error,
    filters,
    fetchBrands,
    updateFilters,
    clearFilters,
  };
}

export function useFeaturedBrands(limit: number = 6) {
  const { brands, loading, error } = useBrands({ 
    initialFilters: { isPartner: true },
    limit 
  });

  const featuredBrands = brands.slice(0, limit);

  return {
    brands: featuredBrands,
    loading,
    error,
  };
}

export function useBrandsByCategory(category: string) {
  const { brands, loading, error, fetchBrands } = useBrands({
    initialFilters: { category }
  });

  return {
    brands,
    loading,
    error,
    refetch: () => fetchBrands({ category }),
  };
}