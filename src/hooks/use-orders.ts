"use client";

import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";
import { OrderResponseDto, CreateOrderRequestDto, UpdateOrderRequestDto } from "@/app/dto";

// Updated Order interface to match DTO
export interface Order extends OrderResponseDto {
  // Additional computed fields for frontend
  statusLabel?: string;
  paymentStatusLabel?: string;
  formattedTotal?: string;
}

// Hook state interface
interface UseOrdersState {
  data: Order[];
  total: number;
  page: number;
  totalPages: number;
  loading: boolean;
  error: string | null;
}

// Main orders hook with pagination and filtering
export function useOrders(params: any = {}) {
  const [state, setState] = useState<UseOrdersState>({
    data: [],
    total: 0,
    page: 1,
    totalPages: 0,
    loading: true,
    error: null,
  });

  const fetchOrders = useCallback(async () => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      
      const queryParams = new URLSearchParams();
      if (params.page) queryParams.append("page", params.page.toString());
      if (params.limit) queryParams.append("limit", params.limit.toString());
      if (params.status) queryParams.append("status", params.status);

      const response = await fetch(`/api/orders?${queryParams}`);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || "Failed to fetch orders");
      }

      setState({
        data: result.data || [],
        total: result.pagination?.total || 0,
        page: result.pagination?.page || 1,
        totalPages: result.pagination?.totalPages || 0,
        loading: false,
        error: null,
      });
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : "Failed to fetch orders",
      }));
      toast.error("Không thể tải danh sách đơn hàng");
    }
  }, [JSON.stringify(params)]);

  useEffect(() => {
    fetchOrders();
  }, [fetchOrders]);

  const refetch = useCallback(() => {
    fetchOrders();
  }, [fetchOrders]);

  return {
    ...state,
    refetch,
  };
}

// Single order hook
export function useOrder(id: string | null) {
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchOrder = useCallback(async () => {
    if (!id) {
      setOrder(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await OrderService.getOrder(id);
      setOrder(response.data || null);
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to fetch order"
      );
      setOrder(null);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchOrder();
  }, [fetchOrder]);

  const refetch = useCallback(() => {
    fetchOrder();
  }, [fetchOrder]);

  return {
    order,
    loading,
    error,
    refetch,
  };
}

// Order by tracking number hook
export function useOrderByTracking(trackingNumber: string | null) {
  const [order, setOrder] = useState<Order | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchOrder = useCallback(async () => {
    if (!trackingNumber) {
      setOrder(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await OrderService.getOrderTracking(trackingNumber);
      setOrder(response.data || null);
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to fetch order"
      );
      setOrder(null);
    } finally {
      setLoading(false);
    }
  }, [trackingNumber]);

  useEffect(() => {
    fetchOrder();
  }, [fetchOrder]);

  const refetch = useCallback(() => {
    fetchOrder();
  }, [fetchOrder]);

  return {
    order,
    loading,
    error,
    refetch,
  };
}

// Create order hook
export function useCreateOrder() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const createOrder = useCallback(async (data: any) => {
    try {
      setLoading(true);
      setError(null);

      const response = await OrderService.createOrder(data);
      return response;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create order";
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    createOrder,
    loading,
    error,
  };
}

// Order statistics hook
export function useOrderStats() {
  const [stats, setStats] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await OrderService.getOrders();
      setStats(response.data);
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to fetch order stats"
      );
      setStats(null);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  const refetch = useCallback(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    stats,
    loading,
    error,
    refetch,
  };
}

// Order timeline hook
export function useOrderTimeline(order: Order | null) {
  const [timeline, setTimeline] = useState<any[]>([]);

  useEffect(() => {
    if (!order) {
      setTimeline([]);
      return;
    }

    const orderTimeline: any[] = [];
    setTimeline(orderTimeline);
  }, [order]);

  return timeline;
}

// Order status utilities hook
export function useOrderStatus(order: Order | null) {
  const statusLabel = order
    ? OrderService.getOrderStatusLabel(order.status)
    : "";
  const progress = order ? OrderService.getOrderProgress(order.status) : 0;
  const canCancel = order ? OrderService.canCancelOrder(order) : false;
  const canReturn = order ? false : false; // OrderService.canReturnOrder not implemented

  return {
    statusLabel,
    progress,
    canCancel,
    canReturn,
  };
}

// Cancel order hook
export function useCancelOrder() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const cancelOrder = useCallback(async (orderId: string, reason?: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await OrderService.cancelOrder(orderId, reason);
      return response;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to cancel order";
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    cancelOrder,
    loading,
    error,
  };
}

// Return order hook
export function useReturnOrder() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const returnOrder = useCallback(async (orderId: string, _reason?: string) => {
    try {
      setLoading(true);
      setError(null);

      const response = await OrderService.updateOrderStatus(
        orderId,
        "CANCELLED"
      ); // returnOrder not implemented
      return response;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to return order";
      setError(errorMessage);
      throw error;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    returnOrder,
    loading,
    error,
  };
}

// Recent orders hook
export function useRecentOrders(limit: number = 5) {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchRecentOrders = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await OrderService.getOrders({ limit });
      setOrders(response.data);
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to fetch recent orders"
      );
      setOrders([]);
    } finally {
      setLoading(false);
    }
  }, [limit]);

  useEffect(() => {
    fetchRecentOrders();
  }, [fetchRecentOrders]);

  const refetch = useCallback(() => {
    fetchRecentOrders();
  }, [fetchRecentOrders]);

  return {
    orders,
    loading,
    error,
    refetch,
  };
}
