"use client";

import { useState, useEffect, useCallback } from "react";
import { Category } from "@/types";
import { CategoryEntity } from "@/app/models/category.model";
import {
  CategoryService,
  CategoryListParams,
  CategoryWithProductCount,
} from "@/lib/services";

// Hook state interface
interface UseCategoriesState {
  data: Category[];
  total: number;
  page: number;
  totalPages: number;
  loading: boolean;
  error: string | null;
}

// Main categories hook with pagination and filtering
export function useCategories(params: CategoryListParams = {}) {
  const [state, setState] = useState<UseCategoriesState>({
    data: [],
    total: 0,
    page: 1,
    totalPages: 0,
    loading: true,
    error: null,
  });

  const fetchCategories = useCallback(async () => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      const response = await CategoryService.getCategories(params);

      setState({
        data: response.data,
        total: response.total,
        page: response.page,
        totalPages: response.totalPages,
        loading: false,
        error: null,
      });
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error:
          error instanceof Error ? error.message : "Failed to fetch categories",
      }));
    }
  }, [JSON.stringify(params)]);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  const refetch = useCallback(() => {
    fetchCategories();
  }, [fetchCategories]);

  return {
    ...state,
    refetch,
  };
}

// Single category hook
export function useCategory(id: string | null) {
  const [category, setCategory] = useState<Category | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCategory = useCallback(async () => {
    if (!id) {
      setCategory(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await CategoryService.getCategory(id);
      setCategory(response.data || null);
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to fetch category"
      );
      setCategory(null);
    } finally {
      setLoading(false);
    }
  }, [id]);

  useEffect(() => {
    fetchCategory();
  }, [fetchCategory]);

  const refetch = useCallback(() => {
    fetchCategory();
  }, [fetchCategory]);

  return {
    category,
    loading,
    error,
    refetch,
  };
}

// Category by slug hook
export function useCategoryBySlug(slug: string | null) {
  const [category, setCategory] = useState<Category | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchCategory = useCallback(async () => {
    if (!slug) {
      setCategory(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const response = await CategoryService.getCategoryBySlug(slug);
      setCategory(response.data || null);
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to fetch category"
      );
      setCategory(null);
    } finally {
      setLoading(false);
    }
  }, [slug]);

  useEffect(() => {
    fetchCategory();
  }, [fetchCategory]);

  const refetch = useCallback(() => {
    fetchCategory();
  }, [fetchCategory]);

  return {
    category,
    loading,
    error,
    refetch,
  };
}

// Root categories hook (no parent)
export function useRootCategories() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchRootCategories = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await CategoryService.getRootCategories();
      setCategories(response.data);
    } catch (error) {
      setError(
        error instanceof Error
          ? error.message
          : "Failed to fetch root categories"
      );
      setCategories([]);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchRootCategories();
  }, [fetchRootCategories]);

  const refetch = useCallback(() => {
    fetchRootCategories();
  }, [fetchRootCategories]);

  return {
    categories,
    loading,
    error,
    refetch,
  };
}

// Category tree hook
export function useCategoryTree() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCategoryTree = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await CategoryService.getCategoryTree();
      setCategories(response.data || []);
    } catch (error) {
      setError(
        error instanceof Error ? error.message : "Failed to fetch category tree"
      );
      setCategories([]);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCategoryTree();
  }, [fetchCategoryTree]);

  const refetch = useCallback(() => {
    fetchCategoryTree();
  }, [fetchCategoryTree]);

  return {
    categories,
    loading,
    error,
    refetch,
  };
}

// Categories with product counts hook
export function useCategoriesWithCounts() {
  const [categories, setCategories] = useState<CategoryWithProductCount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCategoriesWithCounts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await CategoryService.getCategoriesWithCounts();
      setCategories(response.data || []);
    } catch (error) {
      setError(
        error instanceof Error
          ? error.message
          : "Failed to fetch categories with counts"
      );
      setCategories([]);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCategoriesWithCounts();
  }, [fetchCategoriesWithCounts]);

  const refetch = useCallback(() => {
    fetchCategoriesWithCounts();
  }, [fetchCategoriesWithCounts]);

  return {
    categories,
    loading,
    error,
    refetch,
  };
}

// Featured categories hook
export function useFeaturedCategories(limit: number = 6) {
  const [categories, setCategories] = useState<CategoryWithProductCount[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFeaturedCategories = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await CategoryService.getFeaturedCategories(limit);
      setCategories(response.data);
    } catch (error) {
      setError(
        error instanceof Error
          ? error.message
          : "Failed to fetch featured categories"
      );
      setCategories([]);
    } finally {
      setLoading(false);
    }
  }, [limit]);

  useEffect(() => {
    fetchFeaturedCategories();
  }, [fetchFeaturedCategories]);

  const refetch = useCallback(() => {
    fetchFeaturedCategories();
  }, [fetchFeaturedCategories]);

  return {
    categories,
    loading,
    error,
    refetch,
  };
}
