import { useState, useEffect, useCallback } from "react";
import { toast } from "sonner";
import { AttributeValue, AttributeValueFormData } from "../types/attribute";

interface UseAttributeValuesOptions {
  attributeId: string;
  autoFetch?: boolean;
}

interface UseAttributeValuesReturn {
  values: AttributeValue[];
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchValues: () => Promise<void>;
  createValue: (data: AttributeValueFormData) => Promise<AttributeValue | null>;
  updateValue: (id: string, data: Partial<AttributeValueFormData>) => Promise<AttributeValue | null>;
  deleteValue: (id: string) => Promise<boolean>;
  reorderValues: (values: { id: string; sortOrder: number }[]) => Promise<boolean>;
  refresh: () => Promise<void>;
}

export function useAttributeValues(options: UseAttributeValuesOptions): UseAttributeValuesReturn {
  const { attributeId, autoFetch = true } = options;

  const [values, setValues] = useState<AttributeValue[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchValues = useCallback(async () => {
    if (!attributeId) return;

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`/api/admin/attributes/${attributeId}/values`);
      
      if (!response.ok) {
        throw new Error("Failed to fetch attribute values");
      }

      const data = await response.json();
      setValues(data.data || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Có lỗi xảy ra khi tải danh sách giá trị";
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [attributeId]);

  const createValue = useCallback(async (data: AttributeValueFormData): Promise<AttributeValue | null> => {
    if (!attributeId) return null;

    try {
      const response = await fetch(`/api/admin/attributes/${attributeId}/values`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Có lỗi xảy ra khi tạo giá trị");
      }

      const newValue = await response.json();
      setValues(prev => [...prev, newValue].sort((a, b) => a.sortOrder - b.sortOrder));
      toast.success("Tạo giá trị thành công");
      return newValue;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Có lỗi xảy ra khi tạo giá trị";
      setError(errorMessage);
      toast.error(errorMessage);
      return null;
    }
  }, [attributeId]);

  const updateValue = useCallback(async (id: string, data: Partial<AttributeValueFormData>): Promise<AttributeValue | null> => {
    if (!attributeId) return null;

    try {
      const response = await fetch(`/api/admin/attributes/${attributeId}/values/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Có lỗi xảy ra khi cập nhật giá trị");
      }

      const updatedValue = await response.json();
      setValues(prev => 
        prev.map(value => value.id === id ? updatedValue : value)
          .sort((a, b) => a.sortOrder - b.sortOrder)
      );
      toast.success("Cập nhật giá trị thành công");
      return updatedValue;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Có lỗi xảy ra khi cập nhật giá trị";
      setError(errorMessage);
      toast.error(errorMessage);
      return null;
    }
  }, [attributeId]);

  const deleteValue = useCallback(async (id: string): Promise<boolean> => {
    if (!attributeId) return false;

    try {
      const response = await fetch(`/api/admin/attributes/${attributeId}/values/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Có lỗi xảy ra khi xóa giá trị");
      }

      setValues(prev => prev.filter(value => value.id !== id));
      toast.success("Xóa giá trị thành công");
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Có lỗi xảy ra khi xóa giá trị";
      setError(errorMessage);
      toast.error(errorMessage);
      return false;
    }
  }, [attributeId]);

  const reorderValues = useCallback(async (reorderData: { id: string; sortOrder: number }[]): Promise<boolean> => {
    if (!attributeId) return false;

    try {
      const response = await fetch(`/api/admin/attributes/${attributeId}/values/reorder`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          values: reorderData,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Có lỗi xảy ra khi sắp xếp lại thứ tự");
      }

      const result = await response.json();
      setValues(result.data || []);
      toast.success("Cập nhật thứ tự thành công");
      return true;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Có lỗi xảy ra khi sắp xếp lại thứ tự";
      setError(errorMessage);
      toast.error(errorMessage);
      return false;
    }
  }, [attributeId]);

  const refresh = useCallback(async () => {
    await fetchValues();
  }, [fetchValues]);

  // Auto-fetch on mount and when attributeId changes
  useEffect(() => {
    if (autoFetch && attributeId) {
      fetchValues();
    }
  }, [fetchValues, autoFetch, attributeId]);

  return {
    values,
    loading,
    error,
    fetchValues,
    createValue,
    updateValue,
    deleteValue,
    reorderValues,
    refresh,
  };
}
