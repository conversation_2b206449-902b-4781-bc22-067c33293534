/**
 * Inventory Management Types
 */
import { Product } from './index'; // Corrected import path
import { StockMovementType } from '@prisma/client';

// From Prisma Schema: InventoryEntry
export interface InventoryEntry {
  id: string;
  productId: string;
  quantity: number;
  reserved: number;
  available: number;
  minStock: number;
  maxStock?: number | null;
  location?: string | null;
  createdAt: Date;
  updatedAt: Date;
  product?: Product; // Relation
  stockMovements?: InventoryMovement[]; // Relation
}

// Renamed from StockMovement to InventoryMovement for consistency
export interface InventoryMovement {
  id: string;
  inventoryEntryId: string;
  type: StockMovementType;
  quantity: number;
  reason?: string | null;
  reference?: string | null;
  notes?: string | null;
  createdBy?: string | null;
  createdAt: Date;
  inventoryEntry?: InventoryEntry; // Relation
}

// For Admin Page - Combining data for display
export interface InventoryItem extends InventoryEntry {
  product: Product; // Use the full Product type
}

// For Admin Page - Stats
export interface InventoryStats {
  totalProducts: number;
  totalStock: number;
  lowStockProducts: number;
  outOfStockProducts: number;
  totalValue: number;
  alerts: InventoryAlert[];
}

export interface InventoryAlert {
  id: string;
  productId: string;
  productName: string;
  sku: string;
  currentStock: number;
  minStock: number;
  type: 'low_stock' | 'out_of_stock';
  message: string; // Added missing property
  createdAt: Date;
}

// Added for the hook
export interface InventoryPagination {
  page: number;
  limit: number;
}

// Filters for API calls
export interface InventoryFilters {
  search?: string;
  status?: 'in_stock' | 'low_stock' | 'out_of_stock' | 'all'; // Added 'all'
  location?: string;
  page?: number;
  limit?: number;
}

export interface MovementFilters {
  search?: string;
  type?: StockMovementType | 'all'; // Added 'all'
  startDate?: string;
  endDate?: string;
  page?: number;
  limit?: number;
}

// Data for creating/updating
export interface CreateInventoryData {
  productId: string;
  quantity: number;
  minStock?: number;
  location?: string;
}

export interface UpdateInventoryData extends Partial<CreateInventoryData> {}

export interface CreateStockMovementData {
  inventoryEntryId: string;
  type: StockMovementType;
  quantity: number;
  reason?: string;
  reference?: string;
  notes?: string;
}