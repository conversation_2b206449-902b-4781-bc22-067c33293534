/**
 * Public Posts API Routes
 * Handles public access to published posts
 */

import { NextRequest, NextResponse } from "next/server";
import { PostService } from "@/app/api/services/post.service";
import { container } from "@/app/api/di-container";
import { PostSearchFiltersDto } from "@/app/dto";
import { PostStatus } from "@prisma/client";

const postService = container.get<PostService>("PostService");

/**
 * GET /api/posts
 * Get published posts for public consumption
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const filters: PostSearchFiltersDto = {
      search: searchParams.get("search") || undefined,
      categoryId: searchParams.get("categoryId") || undefined,
      status: PostStatus.PUBLISHED, // Only published posts for public
      featured: searchParams.get("featured") === "true" ? true : undefined,
      tags: searchParams.get("tags")?.split(",") || undefined,
      page: parseInt(searchParams.get("page") || "1"),
      limit: parseInt(searchParams.get("limit") || "10"),
      sortBy: (searchParams.get("sortBy") as any) || "publishedAt",
      sortOrder: (searchParams.get("sortOrder") as "asc" | "desc") || "desc",
    };

    const result = await postService.searchPostsWithDto(filters);

    return NextResponse.json({
      success: true,
      posts: result.data,
      pagination: result.pagination,
    });

  } catch (error) {
    console.error("Public posts GET error:", error);
    return NextResponse.json(
      { 
        error: "Internal server error",
        message: "Failed to fetch posts"
      },
      { status: 500 }
    );
  }
}