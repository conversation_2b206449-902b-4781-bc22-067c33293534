/**
 * Public Posts API Routes - Individual Post by Slug
 * Handles public access to specific published posts
 */

import { NextRequest, NextResponse } from "next/server";
import { PostService } from "@/app/api/services/post.service";
import { container } from "@/app/api/di-container";
import { PostStatus } from "@prisma/client";

const postService = container.get<PostService>("PostService");

/**
 * GET /api/posts/[slug]
 * Get single published post by slug
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const post = await postService.getPostBySlugWithDto(params.slug);
    
    if (!post || post.status !== PostStatus.PUBLISHED) {
      return NextResponse.json(
        { error: "Post not found" },
        { status: 404 }
      );
    }

    // Increment view count
    await postService.incrementViewCount(post.id);

    return NextResponse.json({
      success: true,
      post: {
        ...post,
        viewCount: post.viewCount + 1, // Show updated count
      },
    });

  } catch (error) {
    console.error("Public post GET error:", error);
    return NextResponse.json(
      { 
        error: "Internal server error",
        message: "Failed to fetch post"
      },
      { status: 500 }
    );
  }
}