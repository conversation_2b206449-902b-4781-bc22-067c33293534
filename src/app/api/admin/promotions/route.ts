import { NextRequest, NextResponse } from "next/server";
import { verifyAdminAuth } from "../../utils/admin-auth";
import { container, SERVICE_IDENTIFIERS } from "@/app/api/di-container";
import type { PromotionService } from "@/app/api/services/promotion.service";
import { initializeSystem } from "@/app/api/initialize";
import { z } from "zod";
import { PromotionType } from "@prisma/client";

// Validation schema for promotion creation
const createPromotionSchema = z.object({
  name: z.string().min(1, "Tên khuyến mãi là bắt buộc"),
  description: z.string().optional(),
  code: z.string().min(1, "Mã khuyến mãi là bắt buộc"),
  type: z.nativeEnum(PromotionType),
  value: z.number().positive("Giá trị khuyến mãi phải lớn hơn 0"),
  minOrderAmount: z.number().optional(),
  maxDiscountAmount: z.number().optional(),
  usageLimit: z.number().optional(),
  userUsageLimit: z.number().optional(),
  startDate: z.string().transform((str) => new Date(str)),
  endDate: z
    .string()
    .optional()
    .transform((str) => (str ? new Date(str) : undefined)),
  isActive: z.boolean().default(true),
  applicableProducts: z.array(z.string()).default([]),
  applicableCategories: z.array(z.string()).default([]),
  excludedProducts: z.array(z.string()).default([]),
  excludedCategories: z.array(z.string()).default([]),
});

// GET /api/admin/promotions - Get promotions with pagination and filters
export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 });
    }

    // Initialize system and resolve services
    initializeSystem();
    const promotionService = container.resolve<PromotionService>(
      SERVICE_IDENTIFIERS.PROMOTION_SERVICE
    );

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search") || "";
    const type = searchParams.get("type") as PromotionType | null;
    const isActive = searchParams.get("isActive");

    // Build filters
    const filters: any = {
      page,
      limit,
    };

    if (search) {
      filters.search = search;
    }

    if (type) {
      filters.type = type;
    }

    if (isActive !== null && isActive !== undefined) {
      filters.active = isActive === "true";
    }

    // Create admin user for service calls
    const adminUser = {
      id: authResult.admin?.id || "admin",
      role: "ADMIN",
      type: "admin",
    } as any;

    // Get promotions using service
    const result = await promotionService.getPromotions(filters, adminUser);

    return NextResponse.json({
      success: true,
      data: result.data,
      pagination: {
        page: result.page,
        limit: result.limit,
        total: result.total,
        totalPages: result.totalPages,
      },
    });
  } catch (error) {
    console.error("Error fetching promotions:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/admin/promotions - Create new promotion
export async function POST(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 });
    }

    // Initialize system and resolve services
    initializeSystem();
    const promotionService = container.resolve<PromotionService>(
      SERVICE_IDENTIFIERS.PROMOTION_SERVICE
    );

    const body = await request.json();

    // Validate input
    const validatedData = createPromotionSchema.parse(body);

    // Create admin user for service calls
    const adminUser = {
      id: authResult.admin?.id || "admin",
      role: "ADMIN",
      type: "admin",
      name: authResult.admin?.name || "Admin",
      email: authResult.admin?.email || "<EMAIL>",
    } as any;

    // Create promotion data
    const promotionData = {
      ...validatedData,
      createdBy: adminUser.id,
      // Ensure endDate is not undefined
      endDate:
        validatedData.endDate ||
        new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // Default to 1 year from now
    };

    // Create promotion using service
    const promotion = await promotionService.createPromotion(
      promotionData,
      adminUser
    );

    return NextResponse.json({
      success: true,
      data: promotion,
      message: "Khuyến mãi đã được tạo thành công",
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Error creating promotion:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
