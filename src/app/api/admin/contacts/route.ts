import { NextRequest, NextResponse } from "next/server";
import { container, SERVICE_IDENTIFIERS } from "@/app/api/di-container";
import type { ContactService } from "@/app/api/services/contact.service";
import { verifyAdminAuth } from "../../utils/admin-auth";
import { 
  ContactFiltersDtoSchema, 
  CreateContactDtoSchema, 
  UpdateContactDtoSchema,
  contactToResponseDto 
} from "@/app/dto/contact.dto";
import { initializeSystem } from "@/app/api/initialize";



// GET /api/admin/contacts - List contacts with filtering and pagination
export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 });
    }

    // Initialize system
    initializeSystem();
    
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams);
    
    // Convert string params to proper types
    const filters = ContactFiltersDtoSchema.parse({
      ...queryParams,
      page: queryParams.page ? parseInt(queryParams.page) : 1,
      limit: queryParams.limit ? parseInt(queryParams.limit) : 20,
      tags: queryParams.tags ? [queryParams.tags].flat() : undefined,
    });

    const contactService = container.resolve<ContactService>(SERVICE_IDENTIFIERS.CONTACT_SERVICE);

    // Create admin user entity for request
    const adminUserEntity = {
      id: "admin",
      email: "<EMAIL>",
      name: "Admin User",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    // Get contacts with pagination using service
    const result = await contactService.getContacts(
      {
        page: filters.page,
        limit: filters.limit,
        search: filters.search,
        filters: {
          status: filters.status,
          priority: filters.priority,
          assignedTo: filters.assignedTo,
          source: filters.source,
          dateFrom: filters.dateFrom,
          dateTo: filters.dateTo,
          tags: filters.tags,
        },
        sortBy: filters.sortBy,
        sortOrder: filters.sortOrder,
      },
      adminUserEntity
    );

    const totalPages = Math.ceil(result.total / filters.limit);

    return NextResponse.json({
      contacts: result.data.map(contactToResponseDto),
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total: result.total,
        totalPages,
        hasNext: filters.page < totalPages,
        hasPrev: filters.page > 1,
      },
    });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error fetching contacts:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

// POST /api/admin/contacts - Create a new contact (admin only)
export async function POST(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);
    if (!authResult.success) {
      return NextResponse.json({ error: authResult.error }, { status: 401 });
    }

    // Initialize system
    initializeSystem();
    
    const body = await request.json();
    const contactData = CreateContactDtoSchema.parse(body);

    const contactService = container.resolve<ContactService>(SERVICE_IDENTIFIERS.CONTACT_SERVICE);

    // Create admin user entity for audit
    const adminUserEntity = {
      id: "admin",
      email: "<EMAIL>",
      name: "Admin User",
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    } as any;

    const contact = await contactService.createContact(
      contactData,
      adminUserEntity
    );

    return NextResponse.json({
      success: true,
      data: contactToResponseDto(contact),
      message: "Liên hệ đã được tạo thành công",
    }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid contact data", details: error.errors },
        { status: 400 }
      );
    }

    console.error("Error creating contact:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
