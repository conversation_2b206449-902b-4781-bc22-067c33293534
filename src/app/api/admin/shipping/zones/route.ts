import { NextRequest, NextResponse } from "next/server";
import { verifyAdminAuth } from "../../../utils/admin-auth";
import { getService } from "../../di-container";
import { SERVICE_IDENTIFIERS } from "../../di-container/constants";
import { ShippingService } from "../../services/shipping.service";
import { CreateShippingZoneRequestSchema } from "../../../dto/shipping.dto";
import { NotFoundError, ValidationError, ForbiddenError } from "../../../models/common.model";

// GET /api/admin/shipping/zones - Get shipping zones
export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);

    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error || "Không có quyền truy cập",
        },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search") || undefined;

    const shippingService = getService<ShippingService>(SERVICE_IDENTIFIERS.SHIPPING_SERVICE);
    const { data: zones, total, pages } = await shippingService.getAllZones(page, limit, search);

    return NextResponse.json({
      success: true,
      data: zones,
      pagination: {
        page,
        limit,
        total,
        pages,
      },
    });
  } catch (error) {
    console.error("Get shipping zones error:", error);
    let errorMessage = "Có lỗi xảy ra khi lấy danh sách khu vực giao hàng";
    if (error instanceof ValidationError) {
      errorMessage = error.message;
    } else if (error instanceof NotFoundError) {
      errorMessage = error.message;
    } else if (error instanceof ForbiddenError) {
      errorMessage = error.message;
    }
    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: error instanceof ForbiddenError ? 403 : 500 }
    );
  }
}

// POST /api/admin/shipping/zones - Create shipping zone
export async function POST(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request);

    if (!authResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: authResult.error || "Không có quyền truy cập",
        },
        { status: 403 }
      );
    }

    const body = await request.json();
    const validatedData = CreateShippingZoneRequestSchema.parse(body);

    const shippingService = getService<ShippingService>(SERVICE_IDENTIFIERS.SHIPPING_SERVICE);
    const newZone = await shippingService.createZone(validatedData, authResult.adminUser);

    return NextResponse.json({
      success: true,
      data: newZone,
      message: "Khu vực giao hàng đã được tạo thành công",
    });
  } catch (error) {
    console.error("Create shipping zone error:", error);
    let errorMessage = "Có lỗi xảy ra khi tạo khu vực giao hàng";
    if (error instanceof ValidationError) {
      errorMessage = error.message;
    } else if (error instanceof NotFoundError) {
      errorMessage = error.message;
    } else if (error instanceof ForbiddenError) {
      errorMessage = error.message;
    }
    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: error instanceof ForbiddenError ? 403 : 400 } // 400 for validation errors
    );
  }
}
