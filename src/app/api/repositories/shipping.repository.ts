/**
 * Shipping Repositories
 * Manages database operations for ShippingZone and ShippingMethod models.
 */

import { ShippingZone, ShippingMethod, Prisma } from "@prisma/client";
import { BaseRepository } from "./base.repository";
import { CreateShippingZoneData, UpdateShippingZoneData } from "../../models/shipping.model";
import { CreateShippingMethodData, UpdateShippingMethodData } from "../../models/shipping.model";

// --- Shipping Zone Repository ---

export class ShippingZoneRepository extends BaseRepository<
  ShippingZone,
  CreateShippingZoneData,
  UpdateShippingZoneData
> {
  constructor() {
    super("shippingZone");
  }

  async findByName(name: string): Promise<ShippingZone | null> {
    return this.findFirst({ where: { name } });
  }

  async findByProvince(province: string): Promise<ShippingZone | null> {
    return this.findFirst({
      where: {
        provinces: {
          has: province,
        },
        isActive: true,
      },
    });
  }

  async addMethodToZone(zoneId: string, methodData: CreateShippingMethodData): Promise<ShippingMethod> {
    const { zoneId: _zoneId, ...data } = methodData;
    return this.prisma.shippingMethod.create({
      data: {
        ...data,
        zone: { connect: { id: zoneId } },
      },
    });
  }
}

// --- Shipping Method Repository ---

export class ShippingMethodRepository extends BaseRepository<
  ShippingMethod,
  CreateShippingMethodData,
  UpdateShippingMethodData
> {
  constructor() {
    super("shippingMethod");
  }

  async findByZone(zoneId: string): Promise<ShippingMethod[]> {
    return this.findMany({
      where: { zoneId, isActive: true },
      orderBy: { sortOrder: 'asc' },
    });
  }
}
