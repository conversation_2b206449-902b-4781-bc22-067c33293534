/**
 * Payment Gateway Repository
 * Data access layer cho PaymentGateway
 */

import { PrismaClient, PaymentGateway, PaymentProvider } from "@prisma/client";
import { BaseRepository } from "./base.repository";
import {
  PaymentGatewayEntity,
  PaymentGatewaySearchFilters,
} from "../../models/payment.model";
import { PaginationOptions, PaginatedResult } from "../../models/common.model";

export class PaymentGatewayRepository extends BaseRepository {
  constructor(prisma: PrismaClient) {
    super(prisma);
  }

  /**
   * Create payment gateway
   */
  async create(data: Omit<PaymentGatewayEntity, "id" | "createdAt" | "updatedAt">): Promise<PaymentGatewayEntity> {
    try {
      const gateway = await this.prisma.paymentGateway.create({
        data: {
          name: data.name,
          provider: data.provider as PaymentProvider,
          isActive: data.isActive,
          isDefault: data.isDefault,
          config: data.config as any,
          credentials: data.credentials as any,
          supportedMethods: data.supportedMethods,
          fees: data.fees as any,
          limits: data.limits as any,
        },
      });

      return this.mapToEntity(gateway);
    } catch (error) {
      console.error("Error creating payment gateway:", error);
      throw error;
    }
  }

  /**
   * Find payment gateway by ID
   */
  async findById(id: string): Promise<PaymentGatewayEntity | null> {
    try {
      const gateway = await this.prisma.paymentGateway.findUnique({
        where: { id },
        include: {
          transactions: {
            take: 5,
            orderBy: { createdAt: "desc" },
            include: {
              order: {
                select: {
                  id: true,
                  orderNumber: true,
                  total: true,
                  user: {
                    select: {
                      id: true,
                      name: true,
                      email: true,
                    },
                  },
                },
              },
            },
          },
        },
      });

      return gateway ? this.mapToEntityWithRelations(gateway) : null;
    } catch (error) {
      console.error("Error finding payment gateway by ID:", error);
      throw error;
    }
  }

  /**
   * Find all payment gateways with pagination and filtering
   */
  async findMany(
    filters: PaymentGatewaySearchFilters = {},
    pagination: PaginationOptions = {}
  ): Promise<PaginatedResult<PaymentGatewayEntity>> {
    try {
      const { page = 1, limit = 10, sortBy = "createdAt", sortOrder = "desc" } = pagination;
      const { search, provider, isActive, isDefault, supportedMethod } = filters;

      const where: any = {};

      // Search filter
      if (search) {
        where.OR = [
          { name: { contains: search, mode: "insensitive" } },
          { provider: { contains: search, mode: "insensitive" } },
        ];
      }

      // Provider filter
      if (provider) {
        where.provider = provider;
      }

      // Active filter
      if (isActive !== undefined) {
        where.isActive = isActive;
      }

      // Default filter
      if (isDefault !== undefined) {
        where.isDefault = isDefault;
      }

      // Supported method filter
      if (supportedMethod) {
        where.supportedMethods = {
          has: supportedMethod,
        };
      }

      const [gateways, total] = await Promise.all([
        this.prisma.paymentGateway.findMany({
          where,
          skip: (page - 1) * limit,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
          include: {
            transactions: {
              select: { id: true },
            },
          },
        }),
        this.prisma.paymentGateway.count({ where }),
      ]);

      const mappedGateways = gateways.map((gateway) => this.mapToEntity(gateway));

      return this.createPaginatedResult(mappedGateways, total, page, limit);
    } catch (error) {
      console.error("Error finding payment gateways:", error);
      throw error;
    }
  }

  /**
   * Find active gateways
   */
  async findActive(): Promise<PaymentGatewayEntity[]> {
    try {
      const gateways = await this.prisma.paymentGateway.findMany({
        where: { isActive: true },
        orderBy: [
          { isDefault: "desc" },
          { name: "asc" },
        ],
      });

      return gateways.map((gateway) => this.mapToEntity(gateway));
    } catch (error) {
      console.error("Error finding active payment gateways:", error);
      throw error;
    }
  }

  /**
   * Find default gateway
   */
  async findDefault(): Promise<PaymentGatewayEntity | null> {
    try {
      const gateway = await this.prisma.paymentGateway.findFirst({
        where: { 
          isActive: true,
          isDefault: true,
        },
      });

      return gateway ? this.mapToEntity(gateway) : null;
    } catch (error) {
      console.error("Error finding default payment gateway:", error);
      throw error;
    }
  }

  /**
   * Find gateway by provider
   */
  async findByProvider(provider: PaymentProvider): Promise<PaymentGatewayEntity[]> {
    try {
      const gateways = await this.prisma.paymentGateway.findMany({
        where: { 
          provider,
          isActive: true,
        },
        orderBy: { name: "asc" },
      });

      return gateways.map((gateway) => this.mapToEntity(gateway));
    } catch (error) {
      console.error("Error finding payment gateways by provider:", error);
      throw error;
    }
  }

  /**
   * Update payment gateway
   */
  async update(
    id: string,
    data: Partial<Omit<PaymentGatewayEntity, "id" | "createdAt" | "updatedAt">>
  ): Promise<PaymentGatewayEntity> {
    try {
      const updateData: any = {};

      if (data.name !== undefined) updateData.name = data.name;
      if (data.provider !== undefined) updateData.provider = data.provider as PaymentProvider;
      if (data.isActive !== undefined) updateData.isActive = data.isActive;
      if (data.isDefault !== undefined) updateData.isDefault = data.isDefault;
      if (data.config !== undefined) updateData.config = data.config as any;
      if (data.credentials !== undefined) updateData.credentials = data.credentials as any;
      if (data.supportedMethods !== undefined) updateData.supportedMethods = data.supportedMethods;
      if (data.fees !== undefined) updateData.fees = data.fees as any;
      if (data.limits !== undefined) updateData.limits = data.limits as any;

      const gateway = await this.prisma.paymentGateway.update({
        where: { id },
        data: updateData,
      });

      return this.mapToEntity(gateway);
    } catch (error) {
      console.error("Error updating payment gateway:", error);
      throw error;
    }
  }

  /**
   * Delete payment gateway
   */
  async delete(id: string): Promise<boolean> {
    try {
      await this.prisma.paymentGateway.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      console.error("Error deleting payment gateway:", error);
      return false;
    }
  }

  /**
   * Set default gateway
   */
  async setDefault(id: string): Promise<PaymentGatewayEntity> {
    try {
      // First, unset all default gateways
      await this.prisma.paymentGateway.updateMany({
        where: { isDefault: true },
        data: { isDefault: false },
      });

      // Set the specified gateway as default
      const gateway = await this.prisma.paymentGateway.update({
        where: { id },
        data: { 
          isDefault: true,
          isActive: true, // Ensure default gateway is active
        },
      });

      return this.mapToEntity(gateway);
    } catch (error) {
      console.error("Error setting default payment gateway:", error);
      throw error;
    }
  }

  /**
   * Toggle gateway status
   */
  async toggleStatus(id: string): Promise<PaymentGatewayEntity> {
    try {
      const gateway = await this.prisma.paymentGateway.findUnique({
        where: { id },
      });

      if (!gateway) {
        throw new Error("Payment gateway not found");
      }

      const updatedGateway = await this.prisma.paymentGateway.update({
        where: { id },
        data: { 
          isActive: !gateway.isActive,
          // If deactivating default gateway, unset default
          ...(gateway.isDefault && gateway.isActive ? { isDefault: false } : {}),
        },
      });

      return this.mapToEntity(updatedGateway);
    } catch (error) {
      console.error("Error toggling payment gateway status:", error);
      throw error;
    }
  }

  /**
   * Get gateway statistics
   */
  async getStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    byProvider: Record<string, number>;
  }> {
    try {
      const [total, active, byProvider] = await Promise.all([
        this.prisma.paymentGateway.count(),
        this.prisma.paymentGateway.count({ where: { isActive: true } }),
        this.prisma.paymentGateway.groupBy({
          by: ["provider"],
          _count: { provider: true },
        }),
      ]);

      const providerStats = byProvider.reduce((acc, item) => {
        acc[item.provider] = item._count.provider;
        return acc;
      }, {} as Record<string, number>);

      return {
        total,
        active,
        inactive: total - active,
        byProvider: providerStats,
      };
    } catch (error) {
      console.error("Error getting payment gateway stats:", error);
      throw error;
    }
  }

  /**
   * Map database entity to domain entity
   */
  private mapToEntity(gateway: PaymentGateway & { transactions?: any[] }): PaymentGatewayEntity {
    return {
      id: gateway.id,
      name: gateway.name,
      provider: gateway.provider as any,
      isActive: gateway.isActive,
      isDefault: gateway.isDefault,
      config: gateway.config as any,
      credentials: gateway.credentials as any,
      supportedMethods: gateway.supportedMethods,
      fees: gateway.fees as any,
      limits: gateway.limits as any,
      createdAt: gateway.createdAt,
      updatedAt: gateway.updatedAt,
    };
  }

  /**
   * Map database entity with relations to domain entity
   */
  private mapToEntityWithRelations(gateway: any): PaymentGatewayEntity {
    return {
      ...this.mapToEntity(gateway),
      // Additional relations can be mapped here if needed
    };
  }
}