/**
 * Address Repository
 * Quản lý các thao tác database cho Address model, đồng bộ với prisma.schema
 */

import { Address, Prisma } from "@prisma/client";
import { BaseRepository, NotFoundError } from "./base.repository";
import { CreateAddressData, UpdateAddressData } from "../../models/address.model";

export type AddressCreateInput = CreateAddressData;
export type AddressUpdateInput = UpdateAddressData;

export interface AddressSearchOptions {
  search?: string;
  userId?: string;
  province?: string;
  district?: string;
  isDefault?: boolean;
}

export class AddressRepository extends BaseRepository<
  Address,
  AddressCreateInput,
  AddressUpdateInput
> {
  constructor() {
    super("address");
  }

  // T<PERSON>m addresses của user
  async findUserAddresses(userId: string): Promise<Address[]> {
    return this.findMany({
      where: { userId },
      orderBy: [{ isDefault: "desc" }, { createdAt: "desc" }],
    });
  }

  // Alias for findUserAddresses
  async findByUser(userId: string): Promise<Address[]> {
    return this.findUserAddresses(userId);
  }

  // Tìm default address của user
  async findUserDefaultAddress(userId: string): Promise<Address | null> {
    return this.findFirst({
      where: {
        userId,
        isDefault: true,
      },
    });
  }

  // Alias for findUserDefaultAddress
  async findDefaultByUser(userId: string): Promise<Address | null> {
    return this.findUserDefaultAddress(userId);
  }

  // Count addresses by user
  async countByUser(userId: string): Promise<number> {
    return this.count({ where: { userId } });
  }

  // Unset default for user
  async unsetDefaultForUser(userId: string): Promise<void> {
    await this.prisma.address.updateMany({
      where: {
        userId,
        isDefault: true,
      },
      data: { isDefault: false },
    });
  }

  // Tạo address mới
  async createAddress(data: AddressCreateInput): Promise<Address> {
    const { userId, ...addressData } = data;
    return this.model.create({
      data: {
        ...addressData,
        user: { connect: { id: userId } },
      },
    });
  }

  // Cập nhật address
  async updateAddress(id: string, data: AddressUpdateInput): Promise<Address> {
    return this.update(id, data);
  }

  // Đặt address làm default
  async setAsDefault(id: string): Promise<Address> {
    const address = await this.findById(id);
    if (!address) {
      throw new NotFoundError("Address", id);
    }

    // Bỏ default của các address khác cùng user
    await this.unsetDefaultForUser(address.userId);

    return this.update(id, { isDefault: true });
  }

  // Xóa address
  async deleteAddress(id: string): Promise<Address> {
    const address = await this.findById(id);
    if (!address) {
      throw new NotFoundError("Address", id);
    }

    const deletedAddress = await this.delete(id);

    // Nếu xóa default address, đặt address khác làm default
    if (address.isDefault) {
      const nextAddress = await this.findFirst({
        where: { userId: address.userId },
        orderBy: { createdAt: "desc" },
      });

      if (nextAddress) {
        await this.update(nextAddress.id, { isDefault: true });
      }
    }

    return deletedAddress;
  }

  // Tìm kiếm addresses với filter
  async searchAddresses(
    options: AddressSearchOptions & {
      page?: number;
      limit?: number;
    }
  ) {
    const {
      search,
      userId,
      province,
      district,
      isDefault,
      page = 1,
      limit = 20,
    } = options;

    const where: Prisma.AddressWhereInput = {};

    if (search) {
      where.OR = [
        { fullName: { contains: search, mode: "insensitive" } },
        { phone: { contains: search, mode: "insensitive" } },
        { address: { contains: search, mode: "insensitive" } },
        { ward: { contains: search, mode: "insensitive" } },
        { district: { contains: search, mode: "insensitive" } },
        { province: { contains: search, mode: "insensitive" } },
      ];
    }

    if (userId) {
      where.userId = userId;
    }

    if (province) {
      where.province = { contains: province, mode: "insensitive" };
    }

    if (district) {
      where.district = { contains: district, mode: "insensitive" };
    }

    if (typeof isDefault === "boolean") {
      where.isDefault = isDefault;
    }

    return this.findWithPagination({
      page,
      limit,
      where,
      orderBy: [{ isDefault: "desc" }, { createdAt: "desc" }],
    });
  }

  // Format address string
  formatAddressString(address: Address): string {
    return [address.address, address.ward, address.district, address.province]
      .filter(Boolean)
      .join(", ");
  }
}
