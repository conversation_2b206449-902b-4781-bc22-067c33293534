/**
 * Payment Transaction Repository
 * Data access layer cho PaymentTransaction
 */

import { PrismaClient, PaymentTransaction, PaymentMethod, PaymentTransactionStatus } from "@prisma/client";
import { BaseRepository } from "./base.repository";
import {
  PaymentTransactionEntity,
  PaymentTransactionWithRelations,
  PaymentTransactionSearchFilters,
} from "../../models/payment.model";
import { PaginationOptions, PaginatedResult } from "../../models/common.model";

export class PaymentTransactionRepository extends BaseRepository {
  constructor(prisma: PrismaClient) {
    super(prisma);
  }

  /**
   * Create payment transaction
   */
  async create(data: Omit<PaymentTransactionEntity, "id" | "createdAt" | "updatedAt">): Promise<PaymentTransactionEntity> {
    try {
      const transaction = await this.prisma.paymentTransaction.create({
        data: {
          orderId: data.orderId,
          gatewayId: data.gatewayId,
          externalId: data.externalId,
          amount: data.amount,
          currency: data.currency,
          method: data.method as PaymentMethod,
          status: data.status as PaymentTransactionStatus,
          gatewayResponse: data.gatewayResponse as any,
          failureReason: data.failureReason,
          processedAt: data.processedAt,
          refundedAt: data.refundedAt,
          refundAmount: data.refundAmount,
          metadata: data.metadata as any,
        },
      });

      return this.mapToEntity(transaction);
    } catch (error) {
      console.error("Error creating payment transaction:", error);
      throw error;
    }
  }

  /**
   * Find payment transaction by ID
   */
  async findById(id: string): Promise<PaymentTransactionWithRelations | null> {
    try {
      const transaction = await this.prisma.paymentTransaction.findUnique({
        where: { id },
        include: {
          gateway: true,
          order: {
            select: {
              id: true,
              orderNumber: true,
              total: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      return transaction ? this.mapToEntityWithRelations(transaction) : null;
    } catch (error) {
      console.error("Error finding payment transaction by ID:", error);
      throw error;
    }
  }

  /**
   * Find transaction by external ID
   */
  async findByExternalId(externalId: string): Promise<PaymentTransactionEntity | null> {
    try {
      const transaction = await this.prisma.paymentTransaction.findFirst({
        where: { externalId },
      });

      return transaction ? this.mapToEntity(transaction) : null;
    } catch (error) {
      console.error("Error finding payment transaction by external ID:", error);
      throw error;
    }
  }

  /**
   * Find transactions by order ID
   */
  async findByOrderId(orderId: string): Promise<PaymentTransactionEntity[]> {
    try {
      const transactions = await this.prisma.paymentTransaction.findMany({
        where: { orderId },
        orderBy: { createdAt: "desc" },
        include: {
          gateway: true,
        },
      });

      return transactions.map((transaction) => this.mapToEntity(transaction));
    } catch (error) {
      console.error("Error finding payment transactions by order ID:", error);
      throw error;
    }
  }

  /**
   * Find all payment transactions with pagination and filtering
   */
  async findMany(
    filters: PaymentTransactionSearchFilters = {},
    pagination: PaginationOptions = {}
  ): Promise<PaginatedResult<PaymentTransactionWithRelations>> {
    try {
      const { page = 1, limit = 10, sortBy = "createdAt", sortOrder = "desc" } = pagination;
      const { 
        search, 
        orderId, 
        gatewayId, 
        status, 
        method, 
        dateFrom, 
        dateTo, 
        amountMin, 
        amountMax 
      } = filters;

      const where: any = {};

      // Search filter
      if (search) {
        where.OR = [
          { id: { contains: search, mode: "insensitive" } },
          { externalId: { contains: search, mode: "insensitive" } },
          { order: { orderNumber: { contains: search, mode: "insensitive" } } },
        ];
      }

      // Order ID filter
      if (orderId) {
        where.orderId = orderId;
      }

      // Gateway ID filter
      if (gatewayId) {
        where.gatewayId = gatewayId;
      }

      // Status filter
      if (status) {
        where.status = status;
      }

      // Method filter
      if (method) {
        where.method = method;
      }

      // Date range filter
      if (dateFrom || dateTo) {
        where.createdAt = {};
        if (dateFrom) where.createdAt.gte = dateFrom;
        if (dateTo) where.createdAt.lte = dateTo;
      }

      // Amount range filter
      if (amountMin !== undefined || amountMax !== undefined) {
        where.amount = {};
        if (amountMin !== undefined) where.amount.gte = amountMin;
        if (amountMax !== undefined) where.amount.lte = amountMax;
      }

      const [transactions, total] = await Promise.all([
        this.prisma.paymentTransaction.findMany({
          where,
          skip: (page - 1) * limit,
          take: limit,
          orderBy: { [sortBy]: sortOrder },
          include: {
            gateway: true,
            order: {
              select: {
                id: true,
                orderNumber: true,
                total: true,
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  },
                },
              },
            },
          },
        }),
        this.prisma.paymentTransaction.count({ where }),
      ]);

      const mappedTransactions = transactions.map((transaction) => 
        this.mapToEntityWithRelations(transaction)
      );

      return this.createPaginatedResult(mappedTransactions, total, page, limit);
    } catch (error) {
      console.error("Error finding payment transactions:", error);
      throw error;
    }
  }

  /**
   * Update payment transaction
   */
  async update(
    id: string,
    data: Partial<Omit<PaymentTransactionEntity, "id" | "createdAt" | "updatedAt">>
  ): Promise<PaymentTransactionEntity> {
    try {
      const updateData: any = {};

      if (data.status !== undefined) updateData.status = data.status as PaymentTransactionStatus;
      if (data.gatewayResponse !== undefined) updateData.gatewayResponse = data.gatewayResponse as any;
      if (data.failureReason !== undefined) updateData.failureReason = data.failureReason;
      if (data.processedAt !== undefined) updateData.processedAt = data.processedAt;
      if (data.refundedAt !== undefined) updateData.refundedAt = data.refundedAt;
      if (data.refundAmount !== undefined) updateData.refundAmount = data.refundAmount;
      if (data.metadata !== undefined) updateData.metadata = data.metadata as any;

      const transaction = await this.prisma.paymentTransaction.update({
        where: { id },
        data: updateData,
      });

      return this.mapToEntity(transaction);
    } catch (error) {
      console.error("Error updating payment transaction:", error);
      throw error;
    }
  }

  /**
   * Update transaction status
   */
  async updateStatus(
    id: string,
    status: PaymentTransactionStatus,
    gatewayResponse?: Record<string, any>,
    failureReason?: string
  ): Promise<PaymentTransactionEntity> {
    try {
      const updateData: any = { status };

      if (gatewayResponse) {
        updateData.gatewayResponse = gatewayResponse as any;
      }

      if (failureReason) {
        updateData.failureReason = failureReason;
      }

      if (status === PaymentTransactionStatus.SUCCESS) {
        updateData.processedAt = new Date();
      }

      const transaction = await this.prisma.paymentTransaction.update({
        where: { id },
        data: updateData,
      });

      return this.mapToEntity(transaction);
    } catch (error) {
      console.error("Error updating payment transaction status:", error);
      throw error;
    }
  }

  /**
   * Process refund
   */
  async processRefund(
    id: string,
    refundAmount: number,
    status: PaymentTransactionStatus = PaymentTransactionStatus.REFUNDED
  ): Promise<PaymentTransactionEntity> {
    try {
      const transaction = await this.prisma.paymentTransaction.update({
        where: { id },
        data: {
          status,
          refundAmount,
          refundedAt: new Date(),
        },
      });

      return this.mapToEntity(transaction);
    } catch (error) {
      console.error("Error processing refund:", error);
      throw error;
    }
  }

  /**
   * Delete payment transaction
   */
  async delete(id: string): Promise<boolean> {
    try {
      await this.prisma.paymentTransaction.delete({
        where: { id },
      });
      return true;
    } catch (error) {
      console.error("Error deleting payment transaction:", error);
      return false;
    }
  }

  /**
   * Get transaction statistics
   */
  async getStats(dateFrom?: Date, dateTo?: Date): Promise<{
    total: number;
    successful: number;
    failed: number;
    pending: number;
    cancelled: number;
    refunded: number;
    totalAmount: number;
    successfulAmount: number;
    refundedAmount: number;
    byMethod: Record<string, number>;
    byStatus: Record<string, number>;
  }> {
    try {
      const where: any = {};
      
      if (dateFrom || dateTo) {
        where.createdAt = {};
        if (dateFrom) where.createdAt.gte = dateFrom;
        if (dateTo) where.createdAt.lte = dateTo;
      }

      const [
        total,
        successful,
        failed,
        pending,
        cancelled,
        refunded,
        amounts,
        byMethod,
        byStatus,
      ] = await Promise.all([
        this.prisma.paymentTransaction.count({ where }),
        this.prisma.paymentTransaction.count({ 
          where: { ...where, status: PaymentTransactionStatus.SUCCESS } 
        }),
        this.prisma.paymentTransaction.count({ 
          where: { ...where, status: PaymentTransactionStatus.FAILED } 
        }),
        this.prisma.paymentTransaction.count({ 
          where: { ...where, status: PaymentTransactionStatus.PENDING } 
        }),
        this.prisma.paymentTransaction.count({ 
          where: { ...where, status: PaymentTransactionStatus.CANCELLED } 
        }),
        this.prisma.paymentTransaction.count({ 
          where: { 
            ...where, 
            status: { in: [PaymentTransactionStatus.REFUNDED, PaymentTransactionStatus.PARTIAL_REFUND] }
          } 
        }),
        this.prisma.paymentTransaction.aggregate({
          where,
          _sum: {
            amount: true,
            refundAmount: true,
          },
        }),
        this.prisma.paymentTransaction.groupBy({
          by: ["method"],
          where,
          _count: { method: true },
        }),
        this.prisma.paymentTransaction.groupBy({
          by: ["status"],
          where,
          _count: { status: true },
        }),
      ]);

      const successfulAmountData = await this.prisma.paymentTransaction.aggregate({
        where: { ...where, status: PaymentTransactionStatus.SUCCESS },
        _sum: { amount: true },
      });

      const methodStats = byMethod.reduce((acc, item) => {
        acc[item.method] = item._count.method;
        return acc;
      }, {} as Record<string, number>);

      const statusStats = byStatus.reduce((acc, item) => {
        acc[item.status] = item._count.status;
        return acc;
      }, {} as Record<string, number>);

      return {
        total,
        successful,
        failed,
        pending,
        cancelled,
        refunded,
        totalAmount: amounts._sum.amount || 0,
        successfulAmount: successfulAmountData._sum.amount || 0,
        refundedAmount: amounts._sum.refundAmount || 0,
        byMethod: methodStats,
        byStatus: statusStats,
      };
    } catch (error) {
      console.error("Error getting payment transaction stats:", error);
      throw error;
    }
  }

  /**
   * Map database entity to domain entity
   */
  private mapToEntity(transaction: PaymentTransaction): PaymentTransactionEntity {
    return {
      id: transaction.id,
      orderId: transaction.orderId,
      gatewayId: transaction.gatewayId,
      externalId: transaction.externalId,
      amount: transaction.amount,
      currency: transaction.currency,
      method: transaction.method as any,
      status: transaction.status as any,
      gatewayResponse: transaction.gatewayResponse as any,
      failureReason: transaction.failureReason,
      processedAt: transaction.processedAt,
      refundedAt: transaction.refundedAt,
      refundAmount: transaction.refundAmount,
      metadata: transaction.metadata as any,
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
    };
  }

  /**
   * Map database entity with relations to domain entity
   */
  private mapToEntityWithRelations(transaction: any): PaymentTransactionWithRelations {
    return {
      ...this.mapToEntity(transaction),
      gateway: transaction.gateway ? {
        id: transaction.gateway.id,
        name: transaction.gateway.name,
        provider: transaction.gateway.provider,
        isActive: transaction.gateway.isActive,
        isDefault: transaction.gateway.isDefault,
        config: transaction.gateway.config,
        credentials: transaction.gateway.credentials,
        supportedMethods: transaction.gateway.supportedMethods,
        fees: transaction.gateway.fees,
        limits: transaction.gateway.limits,
        createdAt: transaction.gateway.createdAt,
        updatedAt: transaction.gateway.updatedAt,
      } : undefined,
      order: transaction.order,
    };
  }
}