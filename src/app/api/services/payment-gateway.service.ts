/**
 * Payment Gateway Service
 * Business logic layer cho PaymentGateway
 */

import { PaymentGatewayRepository } from "../repositories/payment-gateway.repository";
import {
  PaymentGatewayEntity,
  PaymentGatewaySearchFilters,
  PaymentBusinessRules,
} from "../../models/payment.model";
import { PaginationOptions, PaginatedResult, BusinessError } from "../../models/common.model";

export class PaymentGatewayService {
  constructor(private gatewayRepository: PaymentGatewayRepository) {}

  /**
   * Create new payment gateway
   */
  async createGateway(
    data: Omit<PaymentGatewayEntity, "id" | "createdAt" | "updatedAt">
  ): Promise<PaymentGatewayEntity> {
    try {
      // Validate gateway configuration
      const validation = PaymentBusinessRules.validateGatewayConfig(
        data.provider,
        data.config,
        data.credentials
      );

      if (!validation.valid) {
        throw new BusinessError("Invalid gateway configuration", "INVALID_GATEWAY_CONFIG");
      }

      // If setting as default, ensure it's active
      if (data.isDefault && !data.isActive) {
        throw new BusinessError("Default gateway must be active", "INVALID_DEFAULT_GATEWAY");
      }

      // If setting as default, unset other default gateways
      if (data.isDefault) {
        await this.unsetAllDefaults();
      }

      return await this.gatewayRepository.create(data);
    } catch (error) {
      console.error("Error creating payment gateway:", error);
      throw error;
    }
  }

  /**
   * Get payment gateway by ID
   */
  async getGatewayById(id: string): Promise<PaymentGatewayEntity> {
    try {
      const gateway = await this.gatewayRepository.findById(id);
      
      if (!gateway) {
        throw new BusinessError("Payment gateway not found", "GATEWAY_NOT_FOUND");
      }

      return gateway;
    } catch (error) {
      console.error("Error getting payment gateway:", error);
      throw error;
    }
  }

  /**
   * Get all payment gateways with pagination and filtering
   */
  async getGateways(
    filters: PaymentGatewaySearchFilters = {},
    pagination: PaginationOptions = {}
  ): Promise<PaginatedResult<PaymentGatewayEntity>> {
    try {
      return await this.gatewayRepository.findMany(filters, pagination);
    } catch (error) {
      console.error("Error getting payment gateways:", error);
      throw error;
    }
  }

  /**
   * Get active payment gateways
   */
  async getActiveGateways(): Promise<PaymentGatewayEntity[]> {
    try {
      return await this.gatewayRepository.findActive();
    } catch (error) {
      console.error("Error getting active payment gateways:", error);
      throw error;
    }
  }

  /**
   * Get default payment gateway
   */
  async getDefaultGateway(): Promise<PaymentGatewayEntity | null> {
    try {
      return await this.gatewayRepository.findDefault();
    } catch (error) {
      console.error("Error getting default payment gateway:", error);
      throw error;
    }
  }

  /**
   * Update payment gateway
   */
  async updateGateway(
    id: string,
    data: Partial<Omit<PaymentGatewayEntity, "id" | "createdAt" | "updatedAt">>
  ): Promise<PaymentGatewayEntity> {
    try {
      const existingGateway = await this.getGatewayById(id);

      // If updating configuration, validate it
      if (data.config || data.credentials) {
        const config = { ...existingGateway.config, ...data.config };
        const credentials = { ...existingGateway.credentials, ...data.credentials };
        const provider = data.provider || existingGateway.provider;

        const validation = PaymentBusinessRules.validateGatewayConfig(
          provider,
          config,
          credentials
        );

        if (!validation.valid) {
          throw new BusinessError("Invalid gateway configuration", "INVALID_GATEWAY_CONFIG");
        }
      }

      // If setting as default, ensure it's active
      if (data.isDefault && data.isActive === false) {
        throw new BusinessError("Default gateway must be active", "INVALID_DEFAULT_GATEWAY");
      }

      // If setting as default, unset other default gateways
      if (data.isDefault) {
        await this.unsetAllDefaults();
      }

      return await this.gatewayRepository.update(id, data);
    } catch (error) {
      console.error("Error updating payment gateway:", error);
      throw error;
    }
  }

  /**
   * Delete payment gateway
   */
  async deleteGateway(id: string): Promise<boolean> {
    try {
      const gateway = await this.getGatewayById(id);

      // Cannot delete default gateway
      if (gateway.isDefault) {
        throw new BusinessError("Cannot delete default gateway", "CANNOT_DELETE_DEFAULT_GATEWAY");
      }

      // TODO: Check if gateway has pending transactions
      // const hasPendingTransactions = await this.hasUndingTransactions(id);
      // if (hasPendingTransactions) {
      //   throw new BusinessError("Cannot delete gateway with pending transactions", "HAS_PENDING_TRANSACTIONS");
      // }

      return await this.gatewayRepository.delete(id);
    } catch (error) {
      console.error("Error deleting payment gateway:", error);
      throw error;
    }
  }

  /**
   * Set gateway as default
   */
  async setAsDefault(id: string): Promise<PaymentGatewayEntity> {
    try {
      return await this.gatewayRepository.setDefault(id);
    } catch (error) {
      console.error("Error setting default payment gateway:", error);
      throw error;
    }
  }

  /**
   * Toggle gateway status
   */
  async toggleStatus(id: string): Promise<PaymentGatewayEntity> {
    try {
      return await this.gatewayRepository.toggleStatus(id);
    } catch (error) {
      console.error("Error toggling payment gateway status:", error);
      throw error;
    }
  }

  /**
   * Get gateway statistics
   */
  async getGatewayStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    byProvider: Record<string, number>;
  }> {
    try {
      return await this.gatewayRepository.getStats();
    } catch (error) {
      console.error("Error getting payment gateway statistics:", error);
      throw error;
    }
  }

  /**
   * Get supported payment methods for active gateways
   */
  async getSupportedMethods(): Promise<string[]> {
    try {
      const activeGateways = await this.getActiveGateways();
      
      const allMethods = activeGateways.reduce((methods, gateway) => {
        gateway.supportedMethods.forEach(method => {
          if (!methods.includes(method)) {
            methods.push(method);
          }
        });
        return methods;
      }, [] as string[]);

      return allMethods;
    } catch (error) {
      console.error("Error getting supported payment methods:", error);
      throw error;
    }
  }

  /**
   * Find best gateway for payment method
   */
  async findBestGatewayForMethod(method: string, amount?: number): Promise<PaymentGatewayEntity | null> {
    try {
      const activeGateways = await this.getActiveGateways();
      
      const supportedGateways = activeGateways.filter(gateway => 
        PaymentBusinessRules.isMethodSupported(method as any, gateway)
      );

      if (supportedGateways.length === 0) {
        return null;
      }

      // Filter by amount limits if provided
      let eligibleGateways = supportedGateways;
      if (amount !== undefined) {
        eligibleGateways = supportedGateways.filter(gateway => {
          const validation = PaymentBusinessRules.validateAmount(amount, gateway);
          return validation.valid;
        });
      }

      // Return default gateway if eligible
      const defaultGateway = eligibleGateways.find(gateway => gateway.isDefault);
      if (defaultGateway) {
        return defaultGateway;
      }

      // Return first eligible gateway
      return eligibleGateways[0] || null;
    } catch (error) {
      console.error("Error finding best gateway for method:", error);
      throw error;
    }
  }

  /**
   * Test gateway connection
   */
  async testGatewayConnection(id: string): Promise<{ success: boolean; message: string }> {
    try {
      const gateway = await this.getGatewayById(id);

      // TODO: Implement actual gateway connection testing based on provider
      // This would involve making test API calls to each payment provider

      return {
        success: true,
        message: `Connection test for ${gateway.name} successful`,
      };
    } catch (error) {
      console.error("Error testing gateway connection:", error);
      return {
        success: false,
        message: error instanceof Error ? error.message : "Connection test failed",
      };
    }
  }

  /**
   * Bulk update gateway status
   */
  async bulkUpdateStatus(ids: string[], isActive: boolean): Promise<{
    success: number;
    failed: number;
    errors: Array<{ id: string; error: string }>;
  }> {
    const results = {
      success: 0,
      failed: 0,
      errors: [] as Array<{ id: string; error: string }>,
    };

    for (const id of ids) {
      try {
        await this.gatewayRepository.update(id, { isActive });
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          id,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return results;
  }

  /**
   * Private helper: Unset all default gateways
   */
  private async unsetAllDefaults(): Promise<void> {
    try {
      const defaultGateway = await this.gatewayRepository.findDefault();
      if (defaultGateway) {
        await this.gatewayRepository.update(defaultGateway.id, { isDefault: false });
      }
    } catch (error) {
      console.error("Error unsetting default gateways:", error);
      throw error;
    }
  }
}