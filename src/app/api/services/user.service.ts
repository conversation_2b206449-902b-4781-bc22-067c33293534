/**
 * User Service
 * Business logic cho User management
 */

import { BaseService } from "./base.service";
import { SERVICE_IDENTIFIERS } from "../di-container";
import {
  UserRepository,
  AddressRepository,
  AuditLogRepository,
} from "../repositories";
import { Gender } from "@prisma/client";
import {
  UserEntity,
  UserWithRelations,
  CreateUserData,
  UpdateUserData,
  UserSearchFilters,
  UserBusinessRules,
} from "../../models/user.model";
import { mapPrismaUserToEntity, mapPrismaUsersToEntities, convertGender } from "../../utils/user-mapper";
import {
  PaginatedResult,
  NotFoundError,
  ConflictError,
  ValidationError,
  ForbiddenError,
} from "../../models/common.model";

// Service identifier
export const USER_SERVICE = Symbol("UserService");

/**
 * User Service Class
 */
export class UserService extends BaseService {
  private userRepository: UserRepository;
  private addressRepository: AddressRepository;
  private auditLogRepository: AuditLogRepository;

  constructor() {
    super();
    this.userRepository = this.getRepository<UserRepository>(
      SERVICE_IDENTIFIERS.USER_REPOSITORY
    );
    this.addressRepository = this.getRepository<AddressRepository>(
      SERVICE_IDENTIFIERS.ADDRESS_REPOSITORY
    );
    this.auditLogRepository = this.getRepository<AuditLogRepository>(
      SERVICE_IDENTIFIERS.AUDIT_LOG_REPOSITORY
    );
  }

  /**
   * Tạo user mới
   */
  async createUser(data: CreateUserData, createdBy?: UserEntity): Promise<any> {
    return this.executeWithErrorHandling(async () => {
      // Validate input
      this.validateRequired(data, ["email", "name", "password"]);

      // Validate email format
      if (!UserBusinessRules.validateEmail(data.email)) {
        throw new ValidationError("Invalid email format");
      }

      // Validate password
      const passwordValidation = UserBusinessRules.validatePassword(
        data.password
      );
      if (!passwordValidation.valid) {
        throw new ValidationError("Password validation failed", {
          password: passwordValidation.errors,
        });
      }

      // Check if email already exists
      const existingUser = await this.userRepository.findByEmail(data.email);
      if (existingUser) {
        throw new ConflictError("Email already exists");
      }

      // Hash password
      const hashedPassword = await this.hashPassword(data.password);

      // Create user
      const userData = {
        ...data,
        gender: convertGender(data.gender || null),
        password: hashedPassword,
        isActive: true,
      };

      const prismaUser = await this.userRepository.create(userData);
      const user = mapPrismaUserToEntity(prismaUser);

      // Log action
      this.logAction("USER_CREATED", createdBy, user);

      // Emit event
      await this.emitEvent("user.created", { userId: user.id });

      return user;
    }, "createUser");
  }

  /**
   * Lấy user theo ID
   */
  async getUserById(id: string, requestedBy?: UserEntity): Promise<UserEntity> {
    return this.executeWithErrorHandling(async () => {
      const prismaUser = await this.userRepository.findById(id);
      if (!prismaUser) {
        throw new NotFoundError("User", id);
      }

      const user = mapPrismaUserToEntity(prismaUser);

      // Check permission to view user details
      if (
        requestedBy &&
        !UserBusinessRules.canViewUserDetails(user, requestedBy)
      ) {
        throw new ForbiddenError("Cannot view user details");
      }

      return user;
    }, "getUserById");
  }

  /**
   * Lấy user với relations
   */
  async getUserWithRelations(
    id: string,
    requestedBy?: UserEntity
  ): Promise<UserWithRelations> {
    return this.executeWithErrorHandling(async () => {
      const user = await this.userRepository.findByIdWithRelations(id);
      if (!user) {
        throw new NotFoundError("User", id);
      }

      // Check permission
      if (
        requestedBy &&
        !UserBusinessRules.canViewUserDetails(
          user as unknown as UserEntity,
          requestedBy
        )
      ) {
        throw new ForbiddenError("Cannot view user details");
      }

      return user as unknown as UserWithRelations;
    }, "getUserWithRelations");
  }

  /**
   * Lấy user theo email
   */
  async getUserByEmail(email: string): Promise<UserEntity | null> {
    return this.executeWithErrorHandling(async () => {
      const prismaUser = await this.userRepository.findByEmail(email);
      return prismaUser ? mapPrismaUserToEntity(prismaUser) : null;
    }, "getUserByEmail");
  }

  /**
   * Cập nhật user
   */
  async updateUser(
    id: string,
    data: UpdateUserData,
    updatedBy: UserEntity
  ): Promise<any> {
    return this.executeWithErrorHandling(async () => {
      const user = await this.userRepository.findById(id);
      if (!user) {
        throw new NotFoundError("User", id);
      }

      // Check permission
      if (
        !UserBusinessRules.canUpdateProfile(
          user as unknown as UserEntity,
          updatedBy
        )
      ) {
        throw new ForbiddenError("Cannot update this user");
      }

      // Validate phone if provided
      if (data.phone && !UserBusinessRules.validatePhone(data.phone)) {
        throw new ValidationError("Invalid phone format");
      }

      // Prepare update data with proper types
      const updateData = {
        ...data,
        gender: convertGender(data.gender || null),
        avatarId: data.avatar || undefined,
        avatar: undefined, // Remove avatar field as it should be avatarId
      };

      const updatedPrismaUser = await this.userRepository.update(id, updateData);
      const updatedUser = mapPrismaUserToEntity(updatedPrismaUser);

      // Log action
      this.logAction("USER_UPDATED", updatedBy, updatedUser, data);

      // Emit event
      await this.emitEvent("user.updated", { userId: id, changes: data });

      return updatedUser;
    }, "updateUser");
  }

  /**
   * Cập nhật password
   */
  async updatePassword(
    id: string,
    currentPassword: string,
    newPassword: string,
    updatedBy: UserEntity
  ): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      const user = await this.userRepository.findById(id);
      if (!user) {
        throw new NotFoundError("User", id);
      }

      // Check permission
      if (
        user.id !== updatedBy.id &&
        updatedBy.role !== "ADMIN" &&
        updatedBy.role !== "SUPER_ADMIN"
      ) {
        throw new ForbiddenError("Cannot update password for this user");
      }

      // Verify current password
      if (
        user.password &&
        !(await this.verifyPassword(currentPassword, user.password))
      ) {
        throw new ValidationError("Current password is incorrect");
      }

      // Validate new password
      const passwordValidation =
        UserBusinessRules.validatePassword(newPassword);
      if (!passwordValidation.valid) {
        throw new ValidationError("New password validation failed", {
          password: passwordValidation.errors,
        });
      }

      // Hash new password
      const hashedPassword = await this.hashPassword(newPassword);

      // Update password
      await this.userRepository.update(id, { password: hashedPassword });

      // Log action
      this.logAction("PASSWORD_UPDATED", updatedBy, user);

      // Emit event
      await this.emitEvent("user.password_updated", { userId: id });
    }, "updatePassword");
  }

  /**
   * Deactivate user
   */
  async deactivateUser(
    id: string,
    deactivatedBy: UserEntity
  ): Promise<UserEntity> {
    return this.executeWithErrorHandling(async () => {
      const user = await this.userRepository.findById(id);
      if (!user) {
        throw new NotFoundError("User", id);
      }

      // Check permission
      if (
        !UserBusinessRules.canDeleteUser(
          user as unknown as UserEntity,
          deactivatedBy
        )
      ) {
        throw new ForbiddenError("Cannot deactivate this user");
      }

      const updatedPrismaUser = await this.userRepository.deactivateUser(id);
      const updatedUser = mapPrismaUserToEntity(updatedPrismaUser);

      // Log action
      this.logAction("USER_DEACTIVATED", deactivatedBy, updatedUser);

      // Emit event
      await this.emitEvent("user.deactivated", { userId: id });

      return updatedUser;
    }, "deactivateUser");
  }

  /**
   * Tìm kiếm users
   */
  async searchUsers(
    filters: UserSearchFilters,
    page: number = 1,
    limit: number = 10,
    requestedBy?: UserEntity
  ): Promise<PaginatedResult<UserEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Validate pagination
      this.validatePagination(page, limit);

      // Check permission
      if (
        requestedBy &&
        requestedBy.role !== "ADMIN" &&
        requestedBy.role !== "SUPER_ADMIN"
      ) {
        throw new ForbiddenError("Cannot search users");
      }

      const result = await this.userRepository.findWithPagination({
        page,
        limit,
        where: filters as any,
      });
      return {
        ...result,
        data: mapPrismaUsersToEntities(result.data),
      };
    }, "searchUsers");
  }

  /**
   * Lấy active users
   */
  async getActiveUsers(limit: number = 10): Promise<UserEntity[]> {
    return this.executeWithErrorHandling(async () => {
      const prismaUsers = await this.userRepository.findMany({
        where: { isActive: true },
        take: limit,
      });
      return mapPrismaUsersToEntities(prismaUsers);
    }, "getActiveUsers");
  }

  /**
   * Verify email
   */
  async verifyEmail(userId: string, _token: string): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // TODO: Implement email verification logic
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new NotFoundError("User", userId);
      }

      // Verify token (placeholder)
      // In real implementation, check token validity and expiration

      await this.userRepository.update(userId, {
        // emailVerified: new Date(), // Remove if not in schema
      } as any);

      // Log action
      this.logAction("EMAIL_VERIFIED", user, user);

      // Emit event
      await this.emitEvent("user.email_verified", { userId });
    }, "verifyEmail");
  }

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(email: string): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      const user = await this.userRepository.findByEmail(email);
      if (!user) {
        // Don't reveal if email exists or not
        return;
      }

      // Generate reset token (placeholder)
      const _resetToken = this.generateId();

      // TODO: Store reset token with expiration
      // TODO: Send email with reset link

      // Log action
      this.logAction("PASSWORD_RESET_REQUESTED", undefined, user);

      // Emit event
      await this.emitEvent("user.password_reset_requested", {
        userId: user.id,
        email: user.email,
      });
    }, "sendPasswordResetEmail");
  }

  /**
   * Reset password with token
   */
  async resetPassword(token: string, newPassword: string): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // TODO: Verify token and get user
      // For now, placeholder implementation

      // Validate new password
      const passwordValidation =
        UserBusinessRules.validatePassword(newPassword);
      if (!passwordValidation.valid) {
        throw new ValidationError("Password validation failed", {
          password: passwordValidation.errors,
        });
      }

      // Hash new password
      const _hashedPassword = await this.hashPassword(newPassword);

      // TODO: Update user password and invalidate token

      // Emit event
      await this.emitEvent("user.password_reset", { token });
    }, "resetPassword");
  }

  /**
   * Get user count with filters
   */
  async getUserCount(
    filters: {
      isActive?: boolean;
      role?: string;
      startDate?: Date;
      endDate?: Date;
    } = {}
  ): Promise<number> {
    return this.executeWithErrorHandling(async () => {
      const where: any = {};

      if (filters.isActive !== undefined) {
        where.isActive = filters.isActive;
      }

      if (filters.role) {
        where.role = filters.role;
      }

      if (filters.startDate || filters.endDate) {
        where.createdAt = {};
        if (filters.startDate) {
          where.createdAt.gte = filters.startDate;
        }
        if (filters.endDate) {
          where.createdAt.lte = filters.endDate;
        }
      }

      return await this.userRepository.count(where);
    }, "getUserCount");
  }
}
