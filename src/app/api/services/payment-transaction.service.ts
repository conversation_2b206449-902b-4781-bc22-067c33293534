/**
 * Payment Transaction Service
 * Business logic layer cho PaymentTransaction
 */

import { PaymentTransactionRepository } from "../repositories/payment-transaction.repository";
import { PaymentGatewayService } from "./payment-gateway.service";
import {
  PaymentTransactionEntity,
  PaymentTransactionWithRelations,
  PaymentTransactionSearchFilters,
  PaymentRequest,
  PaymentResponse,
  RefundRequest,
  RefundResponse,
  PaymentBusinessRules,
} from "../../models/payment.model";
import { 
  PaginationOptions, 
  PaginatedResult, 
  BusinessError,
  PaymentTransactionStatus,
  PaymentMethod
} from "../../models/common.model";

export class PaymentTransactionService {
  constructor(
    private transactionRepository: PaymentTransactionRepository,
    private gatewayService: PaymentGatewayService
  ) {}

  /**
   * Create new payment transaction
   */
  async createTransaction(
    data: Omit<PaymentTransactionEntity, "id" | "createdAt" | "updatedAt">
  ): Promise<PaymentTransactionEntity> {
    try {
      // Validate gateway exists and is active
      const gateway = await this.gatewayService.getGatewayById(data.gatewayId);
      if (!gateway.isActive) {
        throw new BusinessError("Payment gateway is not active", "GATEWAY_INACTIVE");
      }

      // Validate payment method is supported
      if (!PaymentBusinessRules.isMethodSupported(data.method, gateway)) {
        throw new BusinessError("Payment method not supported by gateway", "METHOD_NOT_SUPPORTED");
      }

      // Validate amount
      const amountValidation = PaymentBusinessRules.validateAmount(data.amount, gateway);
      if (!amountValidation.valid) {
        throw new BusinessError(amountValidation.error || "Invalid amount", "INVALID_AMOUNT");
      }

      // Generate transaction ID if not provided
      if (!data.externalId) {
        data.externalId = PaymentBusinessRules.generateTransactionId();
      }

      return await this.transactionRepository.create(data);
    } catch (error) {
      console.error("Error creating payment transaction:", error);
      throw error;
    }
  }

  /**
   * Get payment transaction by ID
   */
  async getTransactionById(id: string): Promise<PaymentTransactionWithRelations> {
    try {
      const transaction = await this.transactionRepository.findById(id);
      
      if (!transaction) {
        throw new BusinessError("Payment transaction not found", "TRANSACTION_NOT_FOUND");
      }

      return transaction;
    } catch (error) {
      console.error("Error getting payment transaction:", error);
      throw error;
    }
  }

  /**
   * Get transaction by external ID
   */
  async getTransactionByExternalId(externalId: string): Promise<PaymentTransactionEntity | null> {
    try {
      return await this.transactionRepository.findByExternalId(externalId);
    } catch (error) {
      console.error("Error getting payment transaction by external ID:", error);
      throw error;
    }
  }

  /**
   * Get transactions by order ID
   */
  async getTransactionsByOrderId(orderId: string): Promise<PaymentTransactionEntity[]> {
    try {
      return await this.transactionRepository.findByOrderId(orderId);
    } catch (error) {
      console.error("Error getting payment transactions by order ID:", error);
      throw error;
    }
  }

  /**
   * Get all payment transactions with pagination and filtering
   */
  async getTransactions(
    filters: PaymentTransactionSearchFilters = {},
    pagination: PaginationOptions = {}
  ): Promise<PaginatedResult<PaymentTransactionWithRelations>> {
    try {
      return await this.transactionRepository.findMany(filters, pagination);
    } catch (error) {
      console.error("Error getting payment transactions:", error);
      throw error;
    }
  }

  /**
   * Process payment request
   */
  async processPayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      // Find best gateway for the payment method
      let gateway;
      if (request.gatewayId) {
        gateway = await this.gatewayService.getGatewayById(request.gatewayId);
      } else {
        gateway = await this.gatewayService.findBestGatewayForMethod(
          request.method,
          request.amount
        );
        
        if (!gateway) {
          throw new BusinessError("No suitable payment gateway found", "NO_GATEWAY_FOUND");
        }
      }

      // Create transaction record
      const transaction = await this.createTransaction({
        orderId: request.orderId,
        gatewayId: gateway.id,
        amount: request.amount,
        currency: request.currency,
        method: request.method,
        status: PaymentTransactionStatus.PENDING,
        metadata: request.metadata,
      });

      // TODO: Implement actual payment processing based on gateway provider
      // This would involve calling the actual payment gateway APIs
      
      const paymentResponse: PaymentResponse = {
        success: true,
        transactionId: transaction.id,
        status: PaymentTransactionStatus.PENDING,
        message: "Payment initiated successfully",
      };

      // For COD, mark as success immediately
      if (request.method === PaymentMethod.COD) {
        await this.updateTransactionStatus(
          transaction.id,
          PaymentTransactionStatus.SUCCESS,
          { method: "COD", processed_at: new Date().toISOString() }
        );
        paymentResponse.status = PaymentTransactionStatus.SUCCESS;
        paymentResponse.message = "Cash on delivery order placed successfully";
      }

      return paymentResponse;
    } catch (error) {
      console.error("Error processing payment:", error);
      throw error;
    }
  }

  /**
   * Update payment transaction
   */
  async updateTransaction(
    id: string,
    data: Partial<Omit<PaymentTransactionEntity, "id" | "createdAt" | "updatedAt">>
  ): Promise<PaymentTransactionEntity> {
    try {
      const existingTransaction = await this.getTransactionById(id);

      // Validate status transition if status is being updated
      if (data.status && !PaymentBusinessRules.canTransitionTo(existingTransaction.status as any, data.status)) {
        throw new BusinessError(
          `Cannot transition from ${existingTransaction.status} to ${data.status}`,
          "INVALID_STATUS_TRANSITION"
        );
      }

      return await this.transactionRepository.update(id, data);
    } catch (error) {
      console.error("Error updating payment transaction:", error);
      throw error;
    }
  }

  /**
   * Update transaction status
   */
  async updateTransactionStatus(
    id: string,
    status: PaymentTransactionStatus,
    gatewayResponse?: Record<string, any>,
    failureReason?: string
  ): Promise<PaymentTransactionEntity> {
    try {
      return await this.transactionRepository.updateStatus(id, status, gatewayResponse, failureReason);
    } catch (error) {
      console.error("Error updating transaction status:", error);
      throw error;
    }
  }

  /**
   * Process refund
   */
  async processRefund(request: RefundRequest): Promise<RefundResponse> {
    try {
      const transaction = await this.getTransactionById(request.transactionId);

      // Validate transaction can be refunded
      if (!PaymentBusinessRules.canRefund(transaction as any)) {
        throw new BusinessError("Transaction cannot be refunded", "CANNOT_REFUND");
      }

      // Calculate refund amount
      const refundAmount = request.amount || PaymentBusinessRules.getRefundableAmount(transaction as any);
      
      if (refundAmount <= 0) {
        throw new BusinessError("Invalid refund amount", "INVALID_REFUND_AMOUNT");
      }

      const currentRefunded = transaction.refundAmount || 0;
      if (currentRefunded + refundAmount > transaction.amount) {
        throw new BusinessError("Refund amount exceeds transaction amount", "REFUND_EXCEEDS_AMOUNT");
      }

      // TODO: Implement actual refund processing based on gateway provider
      // This would involve calling the actual payment gateway refund APIs

      // Determine new status
      const totalRefunded = currentRefunded + refundAmount;
      const newStatus = totalRefunded >= transaction.amount 
        ? PaymentTransactionStatus.REFUNDED 
        : PaymentTransactionStatus.PARTIAL_REFUND;

      // Update transaction
      await this.transactionRepository.processRefund(
        request.transactionId,
        totalRefunded,
        newStatus
      );

      return {
        success: true,
        refundId: PaymentBusinessRules.generateTransactionId(),
        refundAmount,
        status: newStatus,
        message: "Refund processed successfully",
      };
    } catch (error) {
      console.error("Error processing refund:", error);
      throw error;
    }
  }

  /**
   * Cancel transaction
   */
  async cancelTransaction(id: string, reason?: string): Promise<PaymentTransactionEntity> {
    try {
      const transaction = await this.getTransactionById(id);

      // Can only cancel pending or processing transactions
      if (![PaymentTransactionStatus.PENDING, PaymentTransactionStatus.PROCESSING].includes(transaction.status as any)) {
        throw new BusinessError("Cannot cancel transaction in current status", "CANNOT_CANCEL");
      }

      return await this.updateTransactionStatus(
        id,
        PaymentTransactionStatus.CANCELLED,
        undefined,
        reason
      );
    } catch (error) {
      console.error("Error cancelling transaction:", error);
      throw error;
    }
  }

  /**
   * Get transaction statistics
   */
  async getTransactionStats(dateFrom?: Date, dateTo?: Date): Promise<{
    total: number;
    successful: number;
    failed: number;
    pending: number;
    cancelled: number;
    refunded: number;
    totalAmount: number;
    successfulAmount: number;
    refundedAmount: number;
    successRate: number;
    averageAmount: number;
    byMethod: Record<string, number>;
    byStatus: Record<string, number>;
  }> {
    try {
      const stats = await this.transactionRepository.getStats(dateFrom, dateTo);

      return {
        ...stats,
        successRate: stats.total > 0 ? (stats.successful / stats.total) * 100 : 0,
        averageAmount: stats.total > 0 ? stats.totalAmount / stats.total : 0,
      };
    } catch (error) {
      console.error("Error getting transaction statistics:", error);
      throw error;
    }
  }

  /**
   * Handle payment webhook
   */
  async handleWebhook(
    gatewayId: string,
    webhookData: Record<string, any>
  ): Promise<{ success: boolean; message: string }> {
    try {
      const gateway = await this.gatewayService.getGatewayById(gatewayId);

      // TODO: Implement webhook handling based on gateway provider
      // This would involve verifying webhook signatures and processing the data

      return {
        success: true,
        message: "Webhook processed successfully",
      };
    } catch (error) {
      console.error("Error handling payment webhook:", error);
      return {
        success: false,
        message: error instanceof Error ? error.message : "Webhook processing failed",
      };
    }
  }

  /**
   * Retry failed transaction
   */
  async retryTransaction(id: string): Promise<PaymentResponse> {
    try {
      const transaction = await this.getTransactionById(id);

      if (transaction.status !== PaymentTransactionStatus.FAILED) {
        throw new BusinessError("Can only retry failed transactions", "CANNOT_RETRY");
      }

      // Reset transaction status to pending
      await this.updateTransactionStatus(id, PaymentTransactionStatus.PENDING);

      // TODO: Implement actual retry logic based on gateway provider

      return {
        success: true,
        transactionId: id,
        status: PaymentTransactionStatus.PENDING,
        message: "Transaction retry initiated",
      };
    } catch (error) {
      console.error("Error retrying transaction:", error);
      throw error;
    }
  }

  /**
   * Get payment receipt data
   */
  async getPaymentReceipt(id: string): Promise<{
    transaction: PaymentTransactionWithRelations;
    gateway: any;
    order?: any;
  }> {
    try {
      const transaction = await this.getTransactionById(id);
      
      if (transaction.status !== PaymentTransactionStatus.SUCCESS) {
        throw new BusinessError("Receipt only available for successful payments", "NO_RECEIPT_AVAILABLE");
      }

      return {
        transaction,
        gateway: transaction.gateway,
        order: transaction.order,
      };
    } catch (error) {
      console.error("Error getting payment receipt:", error);
      throw error;
    }
  }

  /**
   * Bulk update transaction status
   */
  async bulkUpdateStatus(
    ids: string[],
    status: PaymentTransactionStatus,
    reason?: string
  ): Promise<{
    success: number;
    failed: number;
    errors: Array<{ id: string; error: string }>;
  }> {
    const results = {
      success: 0,
      failed: 0,
      errors: [] as Array<{ id: string; error: string }>,
    };

    for (const id of ids) {
      try {
        await this.updateTransactionStatus(id, status, undefined, reason);
        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push({
          id,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return results;
  }
}