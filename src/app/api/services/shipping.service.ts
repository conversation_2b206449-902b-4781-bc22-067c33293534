/**
 * Shipping Service
 * Business logic for ShippingZone and ShippingMethod management.
 */

import { BaseService } from "./base.service";
import { Prisma } from "@prisma/client";

function transformShippingMethod(method: Prisma.ShippingMethodGetPayload<{ include: { zone: true } }>): ShippingMethodEntity {
  return {
    ...method,
    maxDimensions: method.maxDimensions ? (method.maxDimensions as { length: number; width: number; height: number; }) : null,
  };
}

import { SERVICE_IDENTIFIERS } from "../di-container";
import { ShippingZoneRepository, ShippingMethodRepository } from "../repositories";
import {
  ShippingZoneEntity,
  CreateShippingZoneData,
  UpdateShippingZoneData,
  ShippingMethodEntity,
  CreateShippingMethodData,
  UpdateShippingMethodData,
  ShippingBusinessRules,
} from "../../models/shipping.model";
import { NotFoundError, ValidationError, ForbiddenError } from "../../models/common.model";
import { UserEntity } from "../../models/user.model";

export const SHIPPING_SERVICE = Symbol("ShippingService");

export class ShippingService extends BaseService {
  private shippingZoneRepository: ShippingZoneRepository;
  private shippingMethodRepository: ShippingMethodRepository;

  constructor() {
    super();
    this.shippingZoneRepository = this.getRepository<ShippingZoneRepository>(
      SERVICE_IDENTIFIERS.SHIPPING_ZONE_REPOSITORY
    );
    this.shippingMethodRepository = this.getRepository<ShippingMethodRepository>(
      SERVICE_IDENTIFIERS.SHIPPING_METHOD_REPOSITORY
    );
  }

  private isAdmin(user: UserEntity): boolean {
    return ["ADMIN", "SUPER_ADMIN"].includes((user as any).role);
  }

  // --- Shipping Zone Methods ---

  async createZone(data: CreateShippingZoneData, createdBy: UserEntity): Promise<ShippingZoneEntity> {
    return this.executeWithErrorHandling(async () => {
      if (!this.isAdmin(createdBy)) throw new ForbiddenError("Only admins can create shipping zones.");

      const validation = ShippingBusinessRules.validateZone(data);
      if (!validation.valid) throw new ValidationError(validation.errors.join(", "));

      const existingZone = await this.shippingZoneRepository.findByName(data.name);
      if (existingZone) throw new ValidationError(`Shipping zone with name "${data.name}" already exists.`);

      return this.shippingZoneRepository.create(data);
    }, "createZone");
  }

  async updateZone(id: string, data: UpdateShippingZoneData, updatedBy: UserEntity): Promise<ShippingZoneEntity> {
    return this.executeWithErrorHandling(async () => {
      if (!this.isAdmin(updatedBy)) throw new ForbiddenError("Only admins can update shipping zones.");

      const zone = await this.shippingZoneRepository.findById(id);
      if (!zone) throw new NotFoundError("ShippingZone", id);

      const validation = ShippingBusinessRules.validateZone(data);
      if (!validation.valid) throw new ValidationError(validation.errors.join(", "));

      return this.shippingZoneRepository.update(id, data);
    }, "updateZone");
  }

  async deleteZone(id: string, deletedBy: UserEntity): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      if (!this.isAdmin(deletedBy)) throw new ForbiddenError("Only admins can delete shipping zones.");
      
      const zone = await this.shippingZoneRepository.findById(id);
      if (!zone) throw new NotFoundError("ShippingZone", id);

      const zoneWithMethods = await this.shippingZoneRepository.findFirst({ where: { id }, include: { methods: true } });
      if (!zoneWithMethods) throw new NotFoundError("ShippingZone", id);

      if (zoneWithMethods.methods && zoneWithMethods.methods.length > 0) {
        throw new ForbiddenError("Cannot delete shipping zone with associated shipping methods. Please delete methods first.");
      }
      
      // TODO: Add rule to prevent deletion if zone has orders associated with it. (If orders were directly linked to zones)
      
      await this.shippingZoneRepository.delete(id);
    }, "deleteZone");
  }

  async getZoneById(id: string): Promise<ShippingZoneEntity> {
    return this.executeWithErrorHandling(async () => {
      const zone = await this.shippingZoneRepository.findFirst({ where: { id }, include: { methods: true } });
      if (!zone) throw new NotFoundError("ShippingZone", id);
      return zone;
    }, "getZoneById");
  }

  async getAllZones(page: number, limit: number, search?: string): Promise<{ data: ShippingZoneEntity[]; total: number; pages: number; }> {
    return this.executeWithErrorHandling(async () => {
      const skip = (page - 1) * limit;
      const take = limit;

      const where: Prisma.ShippingZoneWhereInput = search
        ? {
            OR: [
              { name: { contains: search, mode: 'insensitive' } },
              { description: { contains: search, mode: 'insensitive' } },
            ],
          }
        : {};

      const [zones, total] = await Promise.all([
        this.shippingZoneRepository.findMany({
          skip,
          take,
          where,
          include: { methods: true },
          orderBy: { name: 'asc' },
        }),
        this.shippingZoneRepository.count({ where }),
      ]);

      return {
        data: zones,
        total,
        pages: Math.ceil(total / limit),
      };
    }, "getAllZones");
  }

  // --- Shipping Method Methods ---

  async createMethod(data: CreateShippingMethodData, createdBy: UserEntity): Promise<ShippingMethodEntity> {
    return this.executeWithErrorHandling(async () => {
      if (!this.isAdmin(createdBy)) throw new ForbiddenError("Only admins can create shipping methods.");

      const validation = ShippingBusinessRules.validateMethod(data);
      if (!validation.valid) throw new ValidationError(validation.errors.join(", "));

      const zone = await this.shippingZoneRepository.findById(data.zoneId);
      if (!zone) throw new NotFoundError("ShippingZone", data.zoneId);

      const newMethod = await this.shippingMethodRepository.create(data);
      const methodWithZone = await this.shippingMethodRepository.findFirst({ where: { id: newMethod.id }, include: { zone: true } });
      if (!methodWithZone) throw new NotFoundError("ShippingMethod", newMethod.id);
      return transformShippingMethod(methodWithZone);
    }, "createMethod");
  }

  async updateMethod(id: string, data: UpdateShippingMethodData, updatedBy: UserEntity): Promise<ShippingMethodEntity> {
    return this.executeWithErrorHandling(async () => {
      if (!this.isAdmin(updatedBy)) throw new ForbiddenError("Only admins can update shipping methods.");

      const method = await this.shippingMethodRepository.findById(id);
      if (!method) throw new NotFoundError("ShippingMethod", id);

      const validation = ShippingBusinessRules.validateMethod(data);
      if (!validation.valid) throw new ValidationError(validation.errors.join(", "));

      const updatedMethod = await this.shippingMethodRepository.update(id, data);
      const methodWithZone = await this.shippingMethodRepository.findFirst({ where: { id: updatedMethod.id }, include: { zone: true } });
      if (!methodWithZone) throw new NotFoundError("ShippingMethod", updatedMethod.id);
      return transformShippingMethod(methodWithZone);
    }, "updateMethod");
  }

  async deleteMethod(id: string, deletedBy: UserEntity): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      if (!this.isAdmin(deletedBy)) throw new ForbiddenError("Only admins can delete shipping methods.");

      const method = await this.shippingMethodRepository.findById(id);
      if (!method) throw new NotFoundError("ShippingMethod", id);

      await this.shippingMethodRepository.delete(id);
    }, "deleteMethod");
  }

  async getMethodsByZone(zoneId?: string, page?: number, limit?: number, search?: string): Promise<{ data: ShippingMethodEntity[]; total: number; pages: number; }> {
    return this.executeWithErrorHandling(async () => {
      const skip = page && limit ? (page - 1) * limit : undefined;
      const take = limit || undefined;

      const where: Prisma.ShippingMethodWhereInput = {
        ...(zoneId && { zoneId }),
        ...(search && {
          OR: [
            { name: { contains: search, mode: 'insensitive' } },
            { description: { contains: search, mode: 'insensitive' } },
          ],
        }),
      };

      const [methods, total] = await Promise.all([
        this.shippingMethodRepository.findMany({
          skip,
          take,
          where,
          include: { zone: true }, // Include zone information
          orderBy: { sortOrder: 'asc' },
        }),
        this.shippingMethodRepository.count({ where }),
      ]);

      return {
        data: methods.map(transformShippingMethod),
        total,
        pages: limit ? Math.ceil(total / limit) : 1,
      };
    }, "getMethodsByZone");
  }

  async getShippingOptions(province: string): Promise<ShippingMethodEntity[]> {
    return this.executeWithErrorHandling(async () => {
      const zone = await this.shippingZoneRepository.findByProvince(province);
      if (!zone) {
        // Return default/national shipping options if no specific zone found
        const nationalZone = await this.shippingZoneRepository.findByName("Toàn quốc"); // Assuming a default zone exists
        if (!nationalZone) return [];
        const methods = await this.shippingMethodRepository.findByZone(nationalZone.id);
        return methods.map(transformShippingMethod);
      }
      const methods = await this.shippingMethodRepository.findByZone(zone.id);
      return methods.map(transformShippingMethod);
    }, "getShippingOptions");
  }
}
