"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Users,
  Search,
  Filter,
  Eye,
  Mail,
  Phone,
  MapPin,
  Calendar,
  ShoppingBag,
  Star,
  Ban,
  CheckCircle,
} from "lucide-react";
import { toast } from "sonner";
import { Gender } from "@/app/models/common.model";
import { AdminDataTable } from "@/lib/admin/components/AdminDataTable";
import { useAdminData, useAdminCrud } from "@/lib/admin/hooks";
import { formatDistanceToNow } from "date-fns";
import { vi } from "date-fns/locale";

interface Customer {
  id: string;
  email: string;
  name: string;
  phone?: string;
  avatar?: string;
  dateOfBirth?: string;
  gender?: Gender;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  _count: {
    orders: number;
    reviews: number;
    addresses: number;
  };
  orders: Array<{
    id: string;
    total: number;
    status: string;
    createdAt: string;
  }>;
  addresses: Array<{
    id: string;
    fullName: string;
    phone: string;
    address: string;
    ward: string;
    district: string;
    province: string;
    isDefault: boolean;
  }>;
}

const genderLabels = {
  MALE: "Nam",
  FEMALE: "Nữ",
  OTHER: "Khác",
};

const orderStatusLabels = {
  PENDING: "Chờ xử lý",
  CONFIRMED: "Đã xác nhận",
  PROCESSING: "Đang xử lý",
  SHIPPED: "Đã giao",
  DELIVERED: "Đã nhận",
  CANCELLED: "Đã hủy",
};

const orderStatusColors = {
  PENDING: "bg-yellow-100 text-yellow-800",
  CONFIRMED: "bg-blue-100 text-blue-800",
  PROCESSING: "bg-purple-100 text-purple-800",
  SHIPPED: "bg-indigo-100 text-indigo-800",
  DELIVERED: "bg-green-100 text-green-800",
  CANCELLED: "bg-red-100 text-red-800",
};

export default function AdminCustomersPage() {
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
    null
  );
  const [filterGender, setFilterGender] = useState<string>("");
  const [searchQuery, setSearchQuery] = useState<string>("");

  // Data fetching
  const {
    data: customers,
    loading,
    error,
    pagination,
    refresh,
    setParams,
    params,
  } = useAdminData<Customer>({
    endpoint: "/api/admin/customers",
    initialParams: { page: 1, limit: 20 },
  });

  // CRUD operations
  const { update: updateCustomer } = useAdminCrud("/api/admin/customers");

  // Handle search
  const handleSearch = (value: string) => {
    setSearchQuery(value);
    setParams({ search: value, page: 1 });
  };

  // Handle filter
  const handleFilterGender = (value: string) => {
    setFilterGender(value);
    setParams({ gender: value === "all" ? undefined : value, page: 1 });
  };

  // Handle view customer details
  const handleViewCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
  };

  // Handle ban/unban customer
  const handleToggleCustomerStatus = async (customer: Customer) => {
    const newStatus = !customer.isActive;
    const result = await updateCustomer(customer.id, { isActive: newStatus });
    if (result) {
      toast.success(
        newStatus ? "Đã kích hoạt khách hàng" : "Đã vô hiệu hóa khách hàng"
      );
      refresh();
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(amount);
  };

  // Calculate customer stats
  const getCustomerStats = (customer: Customer) => {
    // Ensure orders array exists and is not empty
    const orders = customer.orders || [];
    const totalSpent = orders.reduce(
      (sum, order) => sum + (order.total || 0),
      0
    );
    const orderCount = customer._count?.orders || 0;
    const avgOrderValue = orderCount > 0 ? totalSpent / orderCount : 0;
    return { totalSpent, avgOrderValue };
  };

  // Table configuration
  const customerTableConfig = {
    columns: [
      {
        key: "customer",
        title: "Khách hàng",
        sortable: true,
        render: (customer: Customer) => (
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center">
              {customer.avatar ? (
                <img
                  src={customer.avatar}
                  alt={customer.name}
                  className="w-10 h-10 rounded-full object-cover"
                />
              ) : (
                <Users className="h-5 w-5 text-pink-600" />
              )}
            </div>
            <div>
              <div className="font-medium">{customer.name}</div>
              <div className="text-sm text-muted-foreground">
                {customer.email}
              </div>
            </div>
          </div>
        ),
      },
      {
        key: "contact",
        title: "Liên hệ",
        render: (customer: Customer) => (
          <div className="space-y-1">
            {customer.phone && (
              <div className="flex items-center text-sm">
                <Phone className="h-3 w-3 mr-1" />
                {customer.phone}
              </div>
            )}
            <div className="flex items-center text-sm text-muted-foreground">
              <Calendar className="h-3 w-3 mr-1" />
              {customer.gender
                ? genderLabels[customer.gender]
                : "Chưa cập nhật"}
            </div>
          </div>
        ),
      },
      {
        key: "orders",
        title: "Đơn hàng",
        render: (customer: Customer) => {
          const stats = getCustomerStats(customer);
          return (
            <div className="space-y-1">
              <div className="flex items-center text-sm">
                <ShoppingBag className="h-3 w-3 mr-1" />
                {customer._count.orders} đơn
              </div>
              <div className="text-sm text-muted-foreground">
                {formatCurrency(stats.totalSpent)}
              </div>
            </div>
          );
        },
      },
      {
        key: "reviews",
        title: "Đánh giá",
        render: (customer: Customer) => (
          <div className="flex items-center text-sm">
            <Star className="h-3 w-3 mr-1" />
            {customer._count.reviews} đánh giá
          </div>
        ),
      },
      {
        key: "createdAt",
        title: "Ngày tham gia",
        sortable: true,
        render: (customer: Customer) => (
          <div className="text-sm">
            {formatDistanceToNow(new Date(customer.createdAt), {
              addSuffix: true,
              locale: vi,
            })}
          </div>
        ),
      },
    ],
    rowKey: "id",
    actions: {
      enabled: true,
      items: [
        {
          key: "view",
          label: "Xem chi tiết",
          icon: Eye,
          onClick: handleViewCustomer,
        },
        {
          key: "toggle-status",
          label: (customer: Customer) =>
            customer.isActive ? "Vô hiệu hóa" : "Kích hoạt",
          icon: Ban,
          onClick: handleToggleCustomerStatus,
          type: "danger" as const,
        },
      ],
    },
    selection: {
      enabled: true,
      type: "checkbox" as const,
    },
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-pink-100 rounded-lg flex items-center justify-center">
            <Users className="h-5 w-5 text-pink-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold">Quản lý Khách hàng</h1>
            <p className="text-muted-foreground">
              Quản lý thông tin và hoạt động của khách hàng
            </p>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Tổng khách hàng
                </p>
                <p className="text-2xl font-bold">{pagination?.total || 0}</p>
              </div>
              <Users className="h-8 w-8 text-pink-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Khách hàng mới (tháng)
                </p>
                <p className="text-2xl font-bold">0</p>
              </div>
              <Users className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Khách hàng hoạt động
                </p>
                <p className="text-2xl font-bold">0</p>
              </div>
              <CheckCircle className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Tổng doanh thu
                </p>
                <p className="text-2xl font-bold">₫0</p>
              </div>
              <ShoppingBag className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Tìm kiếm theo tên, email, số điện thoại..."
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Select value={filterGender} onValueChange={handleFilterGender}>
                <SelectTrigger className="w-[180px]">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Giới tính" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả</SelectItem>
                  <SelectItem value="MALE">Nam</SelectItem>
                  <SelectItem value="FEMALE">Nữ</SelectItem>
                  <SelectItem value="OTHER">Khác</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Customers Table */}
      <Card>
        <CardHeader>
          <CardTitle>Danh sách Khách hàng</CardTitle>
        </CardHeader>
        <CardContent>
          <AdminDataTable
            dataSource={customers || []}
            config={customerTableConfig}
            loading={loading}
            pagination={
              pagination
                ? {
                    current: pagination.page,
                    pageSize: pagination.limit,
                    total: pagination.total,
                  }
                : undefined
            }
            onPageChange={(page) => setParams({ page })}
            onPageSizeChange={(limit) => setParams({ limit, page: 1 })}
          />
        </CardContent>
      </Card>

      {/* Customer Detail Modal */}
      {selectedCustomer && (
        <Dialog
          open={!!selectedCustomer}
          onOpenChange={() => setSelectedCustomer(null)}
        >
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Chi tiết Khách hàng</DialogTitle>
            </DialogHeader>
            <div className="space-y-6">
              {/* Customer Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Thông tin cá nhân</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center">
                        {selectedCustomer.avatar ? (
                          <img
                            src={selectedCustomer.avatar}
                            alt={selectedCustomer.name}
                            className="w-16 h-16 rounded-full object-cover"
                          />
                        ) : (
                          <Users className="h-8 w-8 text-pink-600" />
                        )}
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg">
                          {selectedCustomer.name}
                        </h3>
                        <p className="text-muted-foreground">
                          {selectedCustomer.email}
                        </p>
                      </div>
                    </div>
                    <div className="space-y-2">
                      {selectedCustomer.phone && (
                        <div className="flex items-center">
                          <Phone className="h-4 w-4 mr-2 text-muted-foreground" />
                          <span>{selectedCustomer.phone}</span>
                        </div>
                      )}
                      {selectedCustomer.gender && (
                        <div className="flex items-center">
                          <Users className="h-4 w-4 mr-2 text-muted-foreground" />
                          <span>{genderLabels[selectedCustomer.gender]}</span>
                        </div>
                      )}
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>
                          Tham gia{" "}
                          {formatDistanceToNow(
                            new Date(selectedCustomer.createdAt),
                            {
                              addSuffix: true,
                              locale: vi,
                            }
                          )}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Thống kê</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {(() => {
                      const stats = getCustomerStats(selectedCustomer);
                      return (
                        <>
                          <div className="flex justify-between">
                            <span>Tổng đơn hàng:</span>
                            <span className="font-semibold">
                              {selectedCustomer._count.orders}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>Tổng chi tiêu:</span>
                            <span className="font-semibold">
                              {formatCurrency(stats.totalSpent)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>Giá trị đơn TB:</span>
                            <span className="font-semibold">
                              {formatCurrency(stats.avgOrderValue)}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>Số đánh giá:</span>
                            <span className="font-semibold">
                              {selectedCustomer._count.reviews}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span>Địa chỉ đã lưu:</span>
                            <span className="font-semibold">
                              {selectedCustomer._count.addresses}
                            </span>
                          </div>
                        </>
                      );
                    })()}
                  </CardContent>
                </Card>
              </div>

              {/* Recent Orders */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Đơn hàng gần đây</CardTitle>
                </CardHeader>
                <CardContent>
                  {selectedCustomer.orders.length > 0 ? (
                    <div className="space-y-3">
                      {selectedCustomer.orders.slice(0, 5).map((order) => (
                        <div
                          key={order.id}
                          className="flex items-center justify-between p-3 border rounded-lg"
                        >
                          <div>
                            <div className="font-medium">
                              #{order.id.slice(-8)}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {formatDistanceToNow(new Date(order.createdAt), {
                                addSuffix: true,
                                locale: vi,
                              })}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="font-semibold">
                              {formatCurrency(order.total)}
                            </div>
                            <Badge
                              variant="secondary"
                              className={
                                orderStatusColors[
                                  order.status as keyof typeof orderStatusColors
                                ]
                              }
                            >
                              {
                                orderStatusLabels[
                                  order.status as keyof typeof orderStatusLabels
                                ]
                              }
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground text-center py-4">
                      Chưa có đơn hàng nào
                    </p>
                  )}
                </CardContent>
              </Card>

              {/* Addresses */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Địa chỉ</CardTitle>
                </CardHeader>
                <CardContent>
                  {selectedCustomer.addresses.length > 0 ? (
                    <div className="space-y-3">
                      {selectedCustomer.addresses.map((address) => (
                        <div key={address.id} className="p-3 border rounded-lg">
                          <div className="flex items-start justify-between">
                            <div className="space-y-1">
                              <div className="font-medium flex items-center gap-2">
                                {address.fullName}
                                {address.isDefault && (
                                  <Badge variant="secondary">Mặc định</Badge>
                                )}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                <Phone className="h-3 w-3 inline mr-1" />
                                {address.phone}
                              </div>
                              <div className="text-sm">
                                <MapPin className="h-3 w-3 inline mr-1" />
                                {address.address}, {address.ward},{" "}
                                {address.district}, {address.province}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-muted-foreground text-center py-4">
                      Chưa có địa chỉ nào
                    </p>
                  )}
                </CardContent>
              </Card>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
