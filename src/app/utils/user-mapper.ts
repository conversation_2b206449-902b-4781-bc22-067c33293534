/**
 * User Type Mappers
 * Chuyển đổi giữa các layer types
 */

import { User as PrismaUser, Gender as PrismaGender } from "@prisma/client";
import { UserEntity, UserWithRelations } from "../models/user.model";
import { UserResponseDto, UserDto } from "../dto/user.dto";
import { UserRole, Gender } from "../models/common.model";

/**
 * Map Prisma User to UserEntity
 */
export function mapPrismaUserToEntity(prismaUser: PrismaUser): UserEntity {
  return {
    id: prismaUser.id,
    email: prismaUser.email,
    name: prismaUser.name,
    password: prismaUser.password,
    avatarId: prismaUser.avatarId,
    phone: prismaUser.phone,
    dateOfBirth: prismaUser.dateOfBirth,
    gender: convertPrismaGenderToGender(prismaUser.gender),
    isActive: prismaUser.isActive,
    createdAt: prismaUser.createdAt,
    updatedAt: prismaUser.updatedAt,
    // These fields are not in Prisma schema but exist in UserEntity
    role: UserRole.USER, // Default role
    emailVerified: undefined,
    phoneVerified: undefined,
    lastLoginAt: undefined,
    preferences: undefined,
    profile: undefined,
  };
}

/**
 * Map UserEntity to UserResponseDto
 */
export function mapUserEntityToDto(userEntity: UserEntity): UserResponseDto {
  return {
    id: userEntity.id,
    email: userEntity.email,
    name: userEntity.name,
    avatar: userEntity.avatarId || undefined,
    phone: userEntity.phone || undefined,
    dateOfBirth: userEntity.dateOfBirth?.toISOString(),
    gender: userEntity.gender || undefined,
    isActive: userEntity.isActive,
    role: userEntity.role || UserRole.USER,
    emailVerified: userEntity.emailVerified?.toISOString(),
    phoneVerified: userEntity.phoneVerified?.toISOString(),
    lastLoginAt: userEntity.lastLoginAt?.toISOString(),
    createdAt: userEntity.createdAt.toISOString(),
    updatedAt: userEntity.updatedAt.toISOString(),
    preferences: userEntity.preferences ? {
      language: userEntity.preferences.language,
      currency: userEntity.preferences.currency,
      timezone: userEntity.preferences.timezone,
      notifications: {
        email: userEntity.preferences.notifications.email,
        sms: userEntity.preferences.notifications.sms,
        push: userEntity.preferences.notifications.push,
        marketing: userEntity.preferences.notifications.marketing,
        orderUpdates: userEntity.preferences.notifications.orderUpdates,
        promotions: userEntity.preferences.notifications.promotions,
      },
      privacy: {
        profileVisibility: userEntity.preferences.privacy.profileVisibility,
        showEmail: userEntity.preferences.privacy.showEmail,
        showPhone: userEntity.preferences.privacy.showPhone,
        allowDataCollection: userEntity.preferences.privacy.allowDataCollection,
      },
    } : undefined,
    profile: userEntity.profile ? {
      bio: userEntity.profile.bio,
      website: userEntity.profile.website,
      location: userEntity.profile.location,
      interests: userEntity.profile.interests,
      socialMedia: userEntity.profile.socialMedia,
    } : undefined,
  };
}

/**
 * Map UserResponseDto to UserEntity
 */
export function mapDtoToUserEntity(dto: UserResponseDto): UserEntity {
  return {
    id: dto.id,
    email: dto.email,
    name: dto.name,
    password: undefined, // Password not included in DTO
    avatarId: dto.avatar || null,
    phone: dto.phone || null,
    dateOfBirth: dto.dateOfBirth ? new Date(dto.dateOfBirth) : null,
    gender: dto.gender || null,
    isActive: dto.isActive,
    role: dto.role,
    emailVerified: dto.emailVerified ? new Date(dto.emailVerified) : undefined,
    phoneVerified: dto.phoneVerified ? new Date(dto.phoneVerified) : undefined,
    lastLoginAt: dto.lastLoginAt ? new Date(dto.lastLoginAt) : undefined,
    createdAt: new Date(dto.createdAt),
    updatedAt: new Date(dto.updatedAt),
    preferences: dto.preferences ? {
      language: dto.preferences.language,
      currency: dto.preferences.currency,
      timezone: dto.preferences.timezone,
      notifications: {
        email: dto.preferences.notifications.email,
        sms: dto.preferences.notifications.sms,
        push: dto.preferences.notifications.push,
        marketing: dto.preferences.notifications.marketing,
        orderUpdates: dto.preferences.notifications.orderUpdates,
        promotions: dto.preferences.notifications.promotions,
      },
      privacy: {
        profileVisibility: dto.preferences.privacy.profileVisibility,
        showEmail: dto.preferences.privacy.showEmail,
        showPhone: dto.preferences.privacy.showPhone,
        allowDataCollection: dto.preferences.privacy.allowDataCollection,
      },
    } : undefined,
    profile: dto.profile ? {
      bio: dto.profile.bio,
      website: dto.profile.website,
      location: dto.profile.location,
      interests: dto.profile.interests,
      socialMedia: dto.profile.socialMedia,
    } : undefined,
  };
}

/**
 * Convert Prisma Gender enum to Model Gender enum
 */
export function convertPrismaGenderToGender(gender: PrismaGender | null): Gender | null {
  if (!gender) return null;
  
  switch (gender) {
    case "MALE":
      return Gender.MALE;
    case "FEMALE":
      return Gender.FEMALE;
    case "OTHER":
      return Gender.OTHER;
    default:
      return null;
  }
}

/**
 * Convert Gender enum between Prisma and Model
 */
export function convertGender(gender: string | null): Gender | null {
  if (!gender) return null;
  
  switch (gender) {
    case "MALE":
      return Gender.MALE;
    case "FEMALE":
      return Gender.FEMALE;
    case "OTHER":
      return Gender.OTHER;
    default:
      return null;
  }
}

/**
 * Map arrays
 */
export function mapPrismaUsersToEntities(prismaUsers: PrismaUser[]): UserEntity[] {
  return prismaUsers.map(mapPrismaUserToEntity);
}

export function mapUserEntitiesToDtos(userEntities: UserEntity[]): UserResponseDto[] {
  return userEntities.map(mapUserEntityToDto);
}