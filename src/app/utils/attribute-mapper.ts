/**
 * Attribute Mapper Utilities
 * Chuyển đổi giữa Prisma Attribute và Business Entity types
 */

import { Attribute as PrismaAttribute, AttributeValue as PrismaAttributeValue, AttributeType } from "@prisma/client";
import { AttributeEntity } from "../models/attribute.model";

type PrismaAttributeWithValues = PrismaAttribute & {
  values?: PrismaAttributeValue[];
  _count?: {
    values: number;
    products: number;
  };
};

/**
 * <PERSON>yển đổi từ Prisma Attribute sang Business Entity
 */
export function mapPrismaAttributeToEntity(
  prismaAttribute: PrismaAttributeWithValues
): AttributeEntity {
  return {
    id: prismaAttribute.id,
    name: prismaAttribute.name,
    slug: prismaAttribute.slug,
    description: prismaAttribute.description || undefined,
    type: prismaAttribute.type as AttributeEntity["type"],
    values: prismaAttribute.values?.map(v => v.value) || [],
    isRequired: prismaAttribute.isRequired,
    isFilterable: prismaAttribute.isFilterable,
    sortOrder: prismaAttribute.sortOrder,
    createdAt: prismaAttribute.createdAt,
    updatedAt: prismaAttribute.updatedAt,
  };
}

/**
 * Chuyển đổi từ Business Entity sang Prisma input
 */
export function mapEntityToPrismaInput(entity: Partial<AttributeEntity>) {
  const { values, ...rest } = entity;
  
  return {
    ...rest,
    description: entity.description || null,
    // values được handle riêng trong repository
  };
}

/**
 * Chuyển đổi AttributeType string sang Prisma enum
 */
export function mapAttributeType(type: string): AttributeType {
  return type as AttributeType;
}

/**
 * Chuyển đổi từ Prisma AttributeValue sang frontend format
 */
export function mapPrismaAttributeValueToFrontend(
  prismaValue: PrismaAttributeValue
) {
  return {
    id: prismaValue.id,
    attributeId: prismaValue.attributeId,
    value: prismaValue.value,
    slug: prismaValue.slug,
    sortOrder: prismaValue.sortOrder,
    createdAt: prismaValue.createdAt.toISOString(),
    updatedAt: prismaValue.updatedAt.toISOString(),
  };
}