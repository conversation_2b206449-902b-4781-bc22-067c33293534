/**
 * Brand Mapper Utilities
 * Chuyển đổi giữa Prisma Brand và Business Entity types
 */

import { Brand as PrismaBrand } from "@prisma/client";
import { BrandEntity } from "../models/brand.model";
import { Status } from "../models/common.model";

/**
 * <PERSON>yển đổi từ Prisma Brand sang Business Entity
 */
export function mapPrismaBrandToEntity(prismaBrand: PrismaBrand): BrandEntity {
  return {
    id: prismaBrand.id,
    name: prismaBrand.name,
    slug: prismaBrand.slug,
    description: prismaBrand.description || undefined,
    logo: prismaBrand.logoId || undefined,
    website: prismaBrand.website || undefined,
    status: prismaBrand.isActive ? Status.ACTIVE : Status.INACTIVE,
    createdAt: prismaBrand.createdAt,
    updatedAt: prismaBrand.updatedAt,
    // Set optional fields to undefined if not provided
    seo: undefined,
    metadata: undefined,
  };
}

/**
 * Chuyển đổi từ Business Entity sang Prisma input
 */
export function mapEntityToPrismaInput(entity: Partial<BrandEntity>) {
  return {
    name: entity.name,
    slug: entity.slug,
    description: entity.description || null,
    logoId: entity.logo || null,
    website: entity.website || null,
    isActive: entity.status === Status.ACTIVE,
  };
}

/**
 * Chuyển đổi Status sang boolean cho Prisma
 */
export function mapStatusToBoolean(status: Status): boolean {
  return status === Status.ACTIVE;
}

/**
 * Chuyển đổi boolean sang Status
 */
export function mapBooleanToStatus(isActive: boolean): Status {
  return isActive ? Status.ACTIVE : Status.INACTIVE;
}