/**
 * Category Type Mappers
 * Chuyển đổi giữa các layer types
 */

import { Category as PrismaCategory, Status } from "@prisma/client";
import { CategoryEntity, CategoryWithRelations } from "../models/category.model";
import { CategoryResponseDto } from "../dto/category.dto";

/**
 * Map Prisma Category to CategoryEntity
 */
export function mapPrismaCategoryToEntity(prismaCategory: PrismaCategory): CategoryEntity {
  return {
    id: prismaCategory.id,
    name: prismaCategory.name,
    slug: prismaCategory.slug,
    description: prismaCategory.description,
    imageId: prismaCategory.imageId,
    parentId: prismaCategory.parentId,
    status: prismaCategory.status,
    sortOrder: prismaCategory.sortOrder,
    mediaId: prismaCategory.mediaId,
    createdAt: prismaCategory.createdAt,
    updatedAt: prismaCategory.updatedAt,
    // These might be extended fields not in Prisma schema
    seo: undefined,
    metadata: undefined,
  };
}

/**
 * Map CategoryEntity to CategoryResponseDto
 */
export function mapCategoryEntityToDto(categoryEntity: CategoryEntity): CategoryResponseDto {
  return {
    id: categoryEntity.id,
    name: categoryEntity.name,
    slug: categoryEntity.slug,
    description: categoryEntity.description || undefined,
    imageId: categoryEntity.imageId || undefined,
    parentId: categoryEntity.parentId || undefined,
    status: categoryEntity.status,
    sortOrder: categoryEntity.sortOrder,
    mediaId: categoryEntity.mediaId || undefined,
    createdAt: categoryEntity.createdAt.toISOString(),
    updatedAt: categoryEntity.updatedAt.toISOString(),
    seo: categoryEntity.seo ? {
      title: categoryEntity.seo.title,
      description: categoryEntity.seo.description,
      keywords: categoryEntity.seo.keywords,
      ogImage: categoryEntity.seo.ogImage,
      canonicalUrl: categoryEntity.seo.canonicalUrl,
    } : undefined,
    metadata: categoryEntity.metadata,
  };
}

/**
 * Map CategoryResponseDto to CategoryEntity
 */
export function mapDtoToCategoryEntity(dto: CategoryResponseDto): CategoryEntity {
  return {
    id: dto.id,
    name: dto.name,
    slug: dto.slug,
    description: dto.description || null,
    imageId: dto.imageId || null,
    parentId: dto.parentId || null,
    status: dto.status,
    sortOrder: dto.sortOrder,
    mediaId: dto.mediaId || null,
    createdAt: new Date(dto.createdAt),
    updatedAt: new Date(dto.updatedAt),
    seo: dto.seo ? {
      title: dto.seo.title,
      description: dto.seo.description,
      keywords: dto.seo.keywords,
      ogImage: dto.seo.ogImage,
      canonicalUrl: dto.seo.canonicalUrl,
    } : undefined,
    metadata: dto.metadata,
  };
}

/**
 * Map Prisma Category with relations to CategoryWithRelations
 */
export function mapPrismaCategoryWithRelationsToEntity(
  prismaCategory: any // Prisma type with relations
): CategoryWithRelations {
  const baseEntity = mapPrismaCategoryToEntity(prismaCategory);
  
  return {
    ...baseEntity,
    parent: prismaCategory.parent ? mapPrismaCategoryToEntity(prismaCategory.parent) : undefined,
    children: prismaCategory.children?.map(mapPrismaCategoryToEntity) || undefined,
    products: prismaCategory.products || undefined,
    stats: prismaCategory._count ? {
      totalProducts: prismaCategory._count.products || 0,
      activeProducts: prismaCategory._count.products || 0, // This might need refinement
      totalSales: 0, // This would need to be calculated separately
      totalRevenue: 0, // This would need to be calculated separately
    } : undefined,
  };
}

/**
 * Map arrays
 */
export function mapPrismaCategoriesToEntities(prismaCategories: PrismaCategory[]): CategoryEntity[] {
  return prismaCategories.map(mapPrismaCategoryToEntity);
}

export function mapCategoryEntitiesToDtos(categoryEntities: CategoryEntity[]): CategoryResponseDto[] {
  return categoryEntities.map(mapCategoryEntityToDto);
}

/**
 * Convert Status enum
 */
export function convertCategoryStatus(status: string | null): Status | null {
  if (!status) return null;
  
  switch (status) {
    case "ACTIVE":
      return Status.ACTIVE;
    case "INACTIVE":
      return Status.INACTIVE;
    case "DRAFT":
      return Status.DRAFT;
    case "ARCHIVED":
      return Status.ARCHIVED;
    default:
      return Status.ACTIVE;
  }
}