/**
 * Promotion DTOs
 * Data Transfer Objects cho Promotion module
 */

import { z } from "zod";
import { PromotionType } from "@prisma/client";

/**
 * Base Promotion DTO Schema
 */
export const PromotionDtoSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  code: z.string(),
  type: z.nativeEnum(PromotionType),
  value: z.number(),
  minOrderAmount: z.number().optional(),
  maxDiscountAmount: z.number().optional(),
  usageLimit: z.number().optional(),
  usageCount: z.number(),
  userUsageLimit: z.number().optional(),
  startDate: z.string(),
  endDate: z.string().optional(),
  isActive: z.boolean(),
  applicableProducts: z.array(z.string()),
  applicableCategories: z.array(z.string()),
  excludedProducts: z.array(z.string()),
  excludedCategories: z.array(z.string()),
  createdBy: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

/**
 * Promotion DTO Type
 */
export type PromotionDto = z.infer<typeof PromotionDtoSchema>;

/**
 * Create Promotion DTO Schema
 */
export const CreatePromotionDtoSchema = z.object({
  name: z.string().min(1, "Tên khuyến mãi là bắt buộc").max(200, "Tên khuyến mãi không được quá 200 ký tự"),
  description: z.string().optional(),
  code: z.string().min(1, "Mã khuyến mãi là bắt buộc").max(50, "Mã khuyến mãi không được quá 50 ký tự"),
  type: z.nativeEnum(PromotionType, {
    errorMap: () => ({ message: "Loại khuyến mãi không hợp lệ" }),
  }),
  value: z.number().positive("Giá trị khuyến mãi phải lớn hơn 0"),
  minOrderAmount: z.number().positive().optional(),
  maxDiscountAmount: z.number().positive().optional(),
  usageLimit: z.number().int().positive().optional(),
  userUsageLimit: z.number().int().positive().optional(),
  startDate: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: "Ngày bắt đầu không hợp lệ",
  }),
  endDate: z.string().optional().refine((date) => !date || !isNaN(Date.parse(date)), {
    message: "Ngày kết thúc không hợp lệ",
  }),
  isActive: z.boolean().default(true),
  applicableProducts: z.array(z.string()).default([]),
  applicableCategories: z.array(z.string()).default([]),
  excludedProducts: z.array(z.string()).default([]),
  excludedCategories: z.array(z.string()).default([]),
}).refine((data) => {
  if (data.endDate) {
    const startDate = new Date(data.startDate);
    const endDate = new Date(data.endDate);
    return startDate < endDate;
  }
  return true;
}, {
  message: "Ngày kết thúc phải sau ngày bắt đầu",
  path: ["endDate"],
}).refine((data) => {
  if (data.type === PromotionType.PERCENTAGE && data.value > 100) {
    return false;
  }
  return true;
}, {
  message: "Giá trị giảm giá theo phần trăm không được vượt quá 100%",
  path: ["value"],
});

/**
 * Create Promotion DTO Type
 */
export type CreatePromotionDto = z.infer<typeof CreatePromotionDtoSchema>;

/**
 * Update Promotion DTO Schema
 */
export const UpdatePromotionDtoSchema = CreatePromotionDtoSchema.omit({
  code: true, // Code cannot be updated
}).partial();

/**
 * Update Promotion DTO Type
 */
export type UpdatePromotionDto = z.infer<typeof UpdatePromotionDtoSchema>;

/**
 * Promotion Response DTO Schema
 */
export const PromotionResponseDtoSchema = PromotionDtoSchema.extend({
  // Additional computed fields
  status: z.enum(["DRAFT", "ACTIVE", "PAUSED", "EXPIRED", "CANCELLED"]),
  isExpired: z.boolean(),
  canUse: z.boolean(),
  discountDisplay: z.string(),
  usagePercentage: z.number().optional(),
});

/**
 * Promotion Response DTO Type
 */
export type PromotionResponseDto = z.infer<typeof PromotionResponseDtoSchema>;

/**
 * Promotion List Response DTO Schema
 */
export const PromotionListResponseDtoSchema = z.object({
  data: z.array(PromotionResponseDtoSchema),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    totalPages: z.number(),
  }),
});

/**
 * Promotion List Response DTO Type
 */
export type PromotionListResponseDto = z.infer<typeof PromotionListResponseDtoSchema>;

/**
 * Promotion Filters DTO Schema
 */
export const PromotionFiltersDtoSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(20),
  search: z.string().optional(),
  type: z.nativeEnum(PromotionType).optional(),
  isActive: z.boolean().optional(),
  status: z.enum(["DRAFT", "ACTIVE", "PAUSED", "EXPIRED", "CANCELLED"]).optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  createdBy: z.string().optional(),
  sortBy: z.enum(["name", "code", "type", "value", "startDate", "endDate", "createdAt"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

/**
 * Promotion Filters DTO Type
 */
export type PromotionFiltersDto = z.infer<typeof PromotionFiltersDtoSchema>;

/**
 * Promotion Usage DTO Schema
 */
export const PromotionUsageDtoSchema = z.object({
  id: z.string(),
  promotionId: z.string(),
  userId: z.string(),
  orderId: z.string(),
  discountAmount: z.number(),
  usedAt: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

/**
 * Promotion Usage DTO Type
 */
export type PromotionUsageDto = z.infer<typeof PromotionUsageDtoSchema>;

/**
 * Promotion Stats DTO Schema
 */
export const PromotionStatsDtoSchema = z.object({
  totalPromotions: z.number(),
  activePromotions: z.number(),
  expiredPromotions: z.number(),
  draftPromotions: z.number(),
  totalUsage: z.number(),
  totalDiscountGiven: z.number(),
  averageDiscountPerOrder: z.number(),
  topPromotions: z.array(z.object({
    id: z.string(),
    name: z.string(),
    code: z.string(),
    usageCount: z.number(),
    totalDiscount: z.number(),
  })),
});

/**
 * Promotion Stats DTO Type
 */
export type PromotionStatsDto = z.infer<typeof PromotionStatsDtoSchema>;

/**
 * Apply Promotion DTO Schema
 */
export const ApplyPromotionDtoSchema = z.object({
  code: z.string().min(1, "Mã khuyến mãi là bắt buộc"),
  orderValue: z.number().positive("Giá trị đơn hàng phải lớn hơn 0"),
  userId: z.string().optional(),
  productIds: z.array(z.string()).default([]),
  categoryIds: z.array(z.string()).default([]),
});

/**
 * Apply Promotion DTO Type
 */
export type ApplyPromotionDto = z.infer<typeof ApplyPromotionDtoSchema>;

/**
 * Promotion Application Result DTO Schema
 */
export const PromotionApplicationResultDtoSchema = z.object({
  success: z.boolean(),
  promotion: PromotionResponseDtoSchema.optional(),
  discountAmount: z.number(),
  finalAmount: z.number(),
  message: z.string(),
  errors: z.array(z.string()).default([]),
});

/**
 * Promotion Application Result DTO Type
 */
export type PromotionApplicationResultDto = z.infer<typeof PromotionApplicationResultDtoSchema>;

/**
 * Bulk Promotion Operations DTO Schema
 */
export const BulkPromotionOperationDtoSchema = z.object({
  ids: z.array(z.string()).min(1, "Ít nhất một khuyến mãi phải được chọn"),
  operation: z.enum(["activate", "deactivate", "delete"]),
});

/**
 * Bulk Promotion Operations DTO Type
 */
export type BulkPromotionOperationDto = z.infer<typeof BulkPromotionOperationDtoSchema>;

/**
 * Promotion Type Labels
 */
export const PromotionTypeLabels: Record<PromotionType, string> = {
  [PromotionType.PERCENTAGE]: "Giảm theo %",
  [PromotionType.FIXED_AMOUNT]: "Giảm cố định",
  [PromotionType.FREE_SHIPPING]: "Miễn phí ship",
  [PromotionType.BUY_X_GET_Y]: "Mua X tặng Y",
};

/**
 * Promotion Status Labels
 */
export const PromotionStatusLabels = {
  DRAFT: "Nháp",
  ACTIVE: "Đang hoạt động",
  PAUSED: "Tạm dừng",
  EXPIRED: "Hết hạn",
  CANCELLED: "Đã hủy",
} as const;