/**
 * Address DTOs
 * Data Transfer Objects cho Address, đồng bộ với prisma.schema
 */

import { z } from 'zod';

/**
 * Address Response DTO
 * Dữ liệu trả về cho API
 */
export interface AddressResponseDto {
  id: string;
  userId: string;
  fullName: string;
  phone: string;
  address: string;
  ward: string;
  district: string;
  province: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
}

/**
 * Create Address Request DTO
 * Dữ liệu nhận vào để tạo địa chỉ
 */
export interface CreateAddressRequestDto {
  fullName: string;
  phone: string;
  address: string;
  ward: string;
  district: string;
  province: string;
  isDefault?: boolean;
}

/**
 * Update Address Request DTO
 * Dữ liệu nhận vào để cập nhật địa chỉ
 */
export type UpdateAddressRequestDto = Partial<CreateAddressRequestDto>;

/**
 * Validation Schemas
 */
export const AddressSchema = z.object({
  fullName: z.string().min(2, 'H<PERSON> và tên phải có ít nhất 2 ký tự'),
  phone: z.string().regex(/^[0-9]{10,11}$/, 'Số điện thoại không hợp lệ (10-11 số)'),
  address: z.string().min(5, 'Địa chỉ (số nhà, đường) phải có ít nhất 5 ký tự'),
  ward: z.string().min(1, 'Phường/Xã là bắt buộc'),
  district: z.string().min(1, 'Quận/Huyện là bắt buộc'),
  province: z.string().min(1, 'Tỉnh/Thành phố là bắt buộc'),
  isDefault: z.boolean().optional(),
});

export const CreateAddressRequestSchema = AddressSchema;
export const UpdateAddressRequestSchema = AddressSchema.partial();

/**
 * Validation functions
 */
export const validateCreateAddress = (data: any): CreateAddressRequestDto => {
  return CreateAddressRequestSchema.parse(data);
};

export const validateUpdateAddress = (data: any): UpdateAddressRequestDto => {
  return UpdateAddressRequestSchema.parse(data);
};
