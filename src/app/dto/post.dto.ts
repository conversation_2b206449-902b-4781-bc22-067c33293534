/**
 * Post DTOs
 * Data Transfer Objects cho Post module
 */

import { PostStatus } from "@prisma/client";

/**
 * Base Post DTO
 */
export interface PostDto {
  id: string;
  title: string;
  content: string;
  excerpt?: string;
  slug: string;
  status: PostStatus;
  featured: boolean;
  featuredImageId?: string;
  featuredImageUrl?: string; // Computed field
  tags: string[];
  categoryId?: string;
  authorId: string;
  viewCount: number;
  commentCount: number;
  publishedAt?: string; // ISO string
  // SEO fields
  metaTitle?: string;
  metaDescription?: string;
  keywords: string[];
  // Metadata
  metadata?: Record<string, any>;
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
}

/**
 * Post with Relations DTO
 */
export interface PostWithRelationsDto extends PostDto {
  author?: {
    id: string;
    name: string;
    email: string;
    avatarId?: string;
    avatarUrl?: string;
  };
  category?: {
    id: string;
    name: string;
    slug: string;
  };
  featuredImage?: {
    id: string;
    url: string;
    alt?: string;
  };
}

/**
 * Create Post DTO
 */
export interface CreatePostDto {
  title: string;
  content: string;
  excerpt?: string;
  slug?: string; // Auto-generated if not provided
  status?: PostStatus;
  featured?: boolean;
  featuredImageId?: string;
  tags?: string[];
  categoryId?: string;
  publishedAt?: string; // ISO string
  // SEO fields
  metaTitle?: string;
  metaDescription?: string;
  keywords?: string[];
  // Metadata
  metadata?: Record<string, any>;
}

/**
 * Update Post DTO
 */
export interface UpdatePostDto {
  title?: string;
  content?: string;
  excerpt?: string;
  slug?: string;
  status?: PostStatus;
  featured?: boolean;
  featuredImageId?: string;
  tags?: string[];
  categoryId?: string;
  publishedAt?: string; // ISO string
  // SEO fields
  metaTitle?: string;
  metaDescription?: string;
  keywords?: string[];
  // Metadata
  metadata?: Record<string, any>;
}

/**
 * Post List Item DTO (for lists/tables)
 */
export interface PostListItemDto {
  id: string;
  title: string;
  excerpt?: string;
  slug: string;
  status: PostStatus;
  featured: boolean;
  featuredImageUrl?: string;
  tags: string[];
  viewCount: number;
  commentCount: number;
  publishedAt?: string;
  createdAt: string;
  updatedAt: string;
  author: {
    id: string;
    name: string;
  };
  category?: {
    id: string;
    name: string;
  };
}

/**
 * Post Search Filters DTO
 */
export interface PostSearchFiltersDto {
  search?: string;
  authorId?: string;
  categoryId?: string;
  status?: PostStatus;
  featured?: boolean;
  tags?: string[];
  dateFrom?: string; // ISO string
  dateTo?: string; // ISO string
  page?: number;
  limit?: number;
  sortBy?: 'createdAt' | 'updatedAt' | 'publishedAt' | 'title' | 'viewCount';
  sortOrder?: 'asc' | 'desc';
}

/**
 * Post Stats DTO
 */
export interface PostStatsDto {
  total: number;
  published: number;
  draft: number;
  archived: number;
  featured: number;
  totalViews: number;
  totalComments: number;
  recentPosts: number; // Posts in last 30 days
}