/**
 * Order DTOs
 * Data Transfer Objects cho Order
 */

import { z } from "zod";
import { OrderStatusDto, PaymentStatusDto, PaymentMethodDto } from "./common.dto";

/**
 * Order Response DTO
 */
export interface OrderResponseDto {
  id: string;
  orderNumber: string;
  userId: string;
  status: OrderStatusDto;
  paymentStatus: PaymentStatusDto;
  total: number;
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  createdAt: string;
  updatedAt: string;
  metadata?: Record<string, any>;
}

/**
 * Create Order Request DTO
 */
export interface CreateOrderRequestDto {
  userId: string;
  items: OrderItemRequestDto[];
  shippingAddress: AddressRequestDto;
  billingAddress?: AddressRequestDto;
  paymentMethod: PaymentMethodDto;
  notes?: string;
}

/**
 * Order Item Request DTO
 */
export interface OrderItemRequestDto {
  productId: string;
  quantity: number;
  price: number;
  variantId?: string;
}

/**
 * Address Request DTO
 */
export interface AddressRequestDto {
  firstName: string;
  lastName: string;
  company?: string;
  address1: string;
  address2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
}

/**
 * Update Order Request DTO
 */
export interface UpdateOrderRequestDto {
  status?: OrderStatusDto;
  paymentStatus?: PaymentStatusDto;
  notes?: string;
  trackingNumber?: string;
}

/**
 * Validation Schemas
 */
export const OrderItemRequestSchema = z.object({
  productId: z.string().uuid("Invalid product ID"),
  quantity: z.number().int().min(1, "Quantity must be at least 1"),
  price: z.number().positive("Price must be positive"),
  variantId: z.string().uuid().optional(),
});

export const AddressRequestSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  company: z.string().optional(),
  address1: z.string().min(1, "Address is required"),
  address2: z.string().optional(),
  city: z.string().min(1, "City is required"),
  state: z.string().min(1, "State is required"),
  postalCode: z.string().min(1, "Postal code is required"),
  country: z.string().min(1, "Country is required"),
  phone: z.string().optional(),
});

export const CreateOrderRequestSchema = z.object({
  userId: z.string().uuid("Invalid user ID"),
  items: z
    .array(OrderItemRequestSchema)
    .min(1, "At least one item is required"),
  shippingAddress: AddressRequestSchema,
  billingAddress: AddressRequestSchema.optional(),
  paymentMethod: z.nativeEnum(PaymentMethodDto),
  notes: z.string().optional(),
});

export const UpdateOrderRequestSchema = z.object({
  status: z.nativeEnum(OrderStatusDto).optional(),
  paymentStatus: z.nativeEnum(PaymentStatusDto).optional(),
  notes: z.string().optional(),
  trackingNumber: z.string().optional(),
});

/**
 * Validation functions
 */
export const validateCreateOrder = (data: any): CreateOrderRequestDto => {
  return CreateOrderRequestSchema.parse(data);
};

export const validateUpdateOrder = (data: any): UpdateOrderRequestDto => {
  return UpdateOrderRequestSchema.parse(data);
};

/**
 * Type aliases for backward compatibility
 */
export type OrderDto = OrderResponseDto;
export type CreateOrderDto = CreateOrderRequestDto;
export type UpdateOrderDto = UpdateOrderRequestDto;
