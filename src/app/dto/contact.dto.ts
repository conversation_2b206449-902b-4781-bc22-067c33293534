/**
 * Contact DTOs
 * Data Transfer Objects cho Contact module
 */

import { z } from "zod";
import { ContactStatus, ContactPriority } from "@prisma/client";

/**
 * Contact DTO Schema
 */
export const ContactDtoSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
  phone: z.string().optional(),
  company: z.string().optional(),
  service: z.string().optional(),
  subject: z.string().optional(),
  message: z.string(),
  status: z.nativeEnum(ContactStatus),
  priority: z.nativeEnum(ContactPriority),
  source: z.string(),
  ipAddress: z.string().optional(),
  userAgent: z.string().optional(),
  assignedTo: z.string().optional(),
  respondedAt: z.string().optional(),
  responseMessage: z.string().optional(),
  tags: z.array(z.string()),
  metadata: z.record(z.any()).optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

/**
 * Contact DTO Type
 */
export type ContactDto = z.infer<typeof ContactDtoSchema>;

/**
 * Contact Response DTO Schema (with relations)
 */
export const ContactResponseDtoSchema = ContactDtoSchema.extend({
  assignedAdmin: z.object({
    id: z.string(),
    name: z.string(),
    email: z.string(),
  }).optional(),
  notes: z.array(z.object({
    id: z.string(),
    note: z.string(),
    isInternal: z.boolean(),
    createdAt: z.string(),
    updatedAt: z.string(),
    admin: z.object({
      id: z.string(),
      name: z.string(),
      email: z.string(),
    }),
  })).default([]),
  notesCount: z.number().default(0),
});

/**
 * Contact Response DTO Type
 */
export type ContactResponseDto = z.infer<typeof ContactResponseDtoSchema>;

/**
 * Create Contact DTO Schema
 */
export const CreateContactDtoSchema = z.object({
  name: z.string().min(1, "Tên là bắt buộc").max(255, "Tên không được quá 255 ký tự"),
  email: z.string().email("Email không hợp lệ"),
  phone: z.string().optional(),
  company: z.string().optional(),
  service: z.string().optional(),
  subject: z.string().min(1, "Chủ đề là bắt buộc").max(255, "Chủ đề không được quá 255 ký tự"),
  message: z.string().min(1, "Tin nhắn là bắt buộc").max(5000, "Tin nhắn không được quá 5000 ký tự"),
  status: z.nativeEnum(ContactStatus).default(ContactStatus.NEW),
  priority: z.nativeEnum(ContactPriority).default(ContactPriority.NORMAL),
  source: z.string().default("website"),
  assignedTo: z.string().optional(),
  tags: z.array(z.string()).default([]),
  metadata: z.record(z.any()).optional(),
});

/**
 * Create Contact DTO Type
 */
export type CreateContactDto = z.infer<typeof CreateContactDtoSchema>;

/**
 * Update Contact DTO Schema
 */
export const UpdateContactDtoSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  email: z.string().email().optional(),
  phone: z.string().optional(),
  company: z.string().optional(),
  service: z.string().optional(),
  subject: z.string().min(1).max(255).optional(),
  message: z.string().min(1).max(5000).optional(),
  status: z.nativeEnum(ContactStatus).optional(),
  priority: z.nativeEnum(ContactPriority).optional(),
  assignedTo: z.string().nullable().optional(),
  responseMessage: z.string().optional(),
  tags: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * Update Contact DTO Type
 */
export type UpdateContactDto = z.infer<typeof UpdateContactDtoSchema>;

/**
 * Contact Filters DTO Schema
 */
export const ContactFiltersDtoSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(20),
  search: z.string().optional(),
  status: z.nativeEnum(ContactStatus).optional(),
  priority: z.nativeEnum(ContactPriority).optional(),
  assignedTo: z.string().optional(),
  source: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  tags: z.array(z.string()).optional(),
  sortBy: z.enum(["createdAt", "updatedAt", "name", "priority", "status", "email"]).default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

/**
 * Contact Filters DTO Type
 */
export type ContactFiltersDto = z.infer<typeof ContactFiltersDtoSchema>;

/**
 * Contact List Response DTO Schema
 */
export const ContactListResponseDtoSchema = z.object({
  data: z.array(ContactResponseDtoSchema),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    totalPages: z.number(),
    hasNext: z.boolean(),
    hasPrev: z.boolean(),
  }),
});

/**
 * Contact List Response DTO Type
 */
export type ContactListResponseDto = z.infer<typeof ContactListResponseDtoSchema>;

/**
 * Contact Note DTO Schema
 */
export const ContactNoteDtoSchema = z.object({
  id: z.string(),
  contactId: z.string(),
  adminId: z.string(),
  note: z.string(),
  isInternal: z.boolean(),
  createdAt: z.string(),
  updatedAt: z.string(),
});

/**
 * Contact Note DTO Type
 */
export type ContactNoteDto = z.infer<typeof ContactNoteDtoSchema>;

/**
 * Create Contact Note DTO Schema
 */
export const CreateContactNoteDtoSchema = z.object({
  note: z.string().min(1, "Ghi chú là bắt buộc").max(2000, "Ghi chú không được quá 2000 ký tự"),
  isInternal: z.boolean().default(true),
});

/**
 * Create Contact Note DTO Type
 */
export type CreateContactNoteDto = z.infer<typeof CreateContactNoteDtoSchema>;

/**
 * Contact Stats DTO Schema
 */
export const ContactStatsDtoSchema = z.object({
  total: z.number(),
  new: z.number(),
  open: z.number(),
  inProgress: z.number(),
  waitingCustomer: z.number(),
  resolved: z.number(),
  closed: z.number(),
  spam: z.number(),
  byPriority: z.object({
    low: z.number(),
    normal: z.number(),
    high: z.number(),
    urgent: z.number(),
  }),
  bySource: z.record(z.number()),
  recentContacts: z.array(ContactResponseDtoSchema.pick({
    id: true,
    name: true,
    email: true,
    subject: true,
    status: true,
    priority: true,
    createdAt: true,
  })),
  responseTime: z.object({
    average: z.number(), // in hours
    median: z.number(),
  }),
});

/**
 * Contact Stats DTO Type
 */
export type ContactStatsDto = z.infer<typeof ContactStatsDtoSchema>;

/**
 * Bulk Contact Operations DTO Schema
 */
export const BulkContactOperationDtoSchema = z.object({
  ids: z.array(z.string()).min(1, "Ít nhất một liên hệ phải được chọn"),
  operation: z.enum(["updateStatus", "assignTo", "addTags", "removeTags", "delete"]),
  data: z.record(z.any()).optional(),
});

/**
 * Bulk Contact Operations DTO Type
 */
export type BulkContactOperationDto = z.infer<typeof BulkContactOperationDtoSchema>;

/**
 * Contact Status Labels
 */
export const ContactStatusLabels: Record<ContactStatus, string> = {
  [ContactStatus.NEW]: "Mới",
  [ContactStatus.OPEN]: "Đang xử lý",
  [ContactStatus.IN_PROGRESS]: "Đang thực hiện",
  [ContactStatus.WAITING_CUSTOMER]: "Chờ khách hàng",
  [ContactStatus.RESOLVED]: "Đã giải quyết",
  [ContactStatus.CLOSED]: "Đã đóng",
  [ContactStatus.SPAM]: "Spam",
};

/**
 * Contact Priority Labels
 */
export const ContactPriorityLabels: Record<ContactPriority, string> = {
  [ContactPriority.LOW]: "Thấp",
  [ContactPriority.NORMAL]: "Bình thường",
  [ContactPriority.HIGH]: "Cao",
  [ContactPriority.URGENT]: "Khẩn cấp",
};

/**
 * Contact Status Colors
 */
export const ContactStatusColors: Record<ContactStatus, string> = {
  [ContactStatus.NEW]: "bg-blue-100 text-blue-800",
  [ContactStatus.OPEN]: "bg-yellow-100 text-yellow-800",
  [ContactStatus.IN_PROGRESS]: "bg-orange-100 text-orange-800",
  [ContactStatus.WAITING_CUSTOMER]: "bg-purple-100 text-purple-800",
  [ContactStatus.RESOLVED]: "bg-green-100 text-green-800",
  [ContactStatus.CLOSED]: "bg-gray-100 text-gray-800",
  [ContactStatus.SPAM]: "bg-red-100 text-red-800",
};

/**
 * Contact Priority Colors
 */
export const ContactPriorityColors: Record<ContactPriority, string> = {
  [ContactPriority.LOW]: "bg-gray-100 text-gray-800",
  [ContactPriority.NORMAL]: "bg-blue-100 text-blue-800",
  [ContactPriority.HIGH]: "bg-orange-100 text-orange-800",
  [ContactPriority.URGENT]: "bg-red-100 text-red-800",
};