/**
 * Promotion Transform Functions
 * Chuyển đổi giữa PromotionEntity và PromotionDto
 */

import { PromotionEntity, PromotionStatus } from "../../models/promotion.model";
import { 
  PromotionDto, 
  PromotionResponseDto, 
  CreatePromotionDto, 
  UpdatePromotionDto,
  PromotionTypeLabels 
} from "../promotion.dto";
import { PromotionType } from "@prisma/client";
import { removeUndefined } from "./common.transform";

/**
 * Transform PromotionEntity to PromotionDto
 */
export function promotionToDto(promotion: PromotionEntity): PromotionDto {
  return removeUndefined({
    id: promotion.id,
    name: promotion.name,
    description: promotion.description,
    code: promotion.code || "",
    type: promotion.type,
    value: promotion.value,
    minOrderAmount: promotion.minOrderValue,
    maxDiscountAmount: promotion.maxDiscountValue,
    usageLimit: promotion.usageLimit,
    usageCount: promotion.usageCount,
    userUsageLimit: promotion.userUsageLimit,
    startDate: promotion.startDate.toISOString(),
    endDate: promotion.endDate?.toISOString(),
    isActive: promotion.isActive,
    applicableProducts: promotion.applicableProducts || [],
    applicableCategories: promotion.applicableCategories || [],
    excludedProducts: promotion.excludedProducts || [],
    excludedCategories: promotion.excludedCategories || [],
    createdBy: promotion.createdBy,
    createdAt: promotion.createdAt.toISOString(),
    updatedAt: promotion.updatedAt.toISOString(),
  }) as PromotionDto;
}

/**
 * Transform PromotionEntity to PromotionResponseDto with computed fields
 */
export function promotionToResponseDto(promotion: PromotionEntity): PromotionResponseDto {
  const baseDto = promotionToDto(promotion);
  const now = new Date();
  
  // Calculate status
  const status = getPromotionStatus(promotion);
  
  // Check if expired
  const isExpired = promotion.endDate ? now > promotion.endDate : false;
  
  // Check if can use
  const canUse = canUsePromotion(promotion);
  
  // Format discount display
  const discountDisplay = formatDiscountDisplay(promotion);
  
  // Calculate usage percentage
  const usagePercentage = promotion.usageLimit 
    ? Math.round((promotion.usageCount / promotion.usageLimit) * 100)
    : undefined;

  return {
    ...baseDto,
    status,
    isExpired,
    canUse,
    discountDisplay,
    usagePercentage,
  };
}

/**
 * Transform CreatePromotionDto to PromotionEntity data
 */
export function createPromotionDtoToEntity(
  dto: CreatePromotionDto,
  createdBy: string
): Omit<PromotionEntity, 'id' | 'createdAt' | 'updatedAt' | 'usageCount' | 'status'> {
  return removeUndefined({
    name: dto.name,
    description: dto.description,
    code: dto.code,
    type: dto.type,
    value: dto.value,
    isPercentage: dto.type === PromotionType.PERCENTAGE,
    minOrderValue: dto.minOrderAmount,
    maxDiscountValue: dto.maxDiscountAmount,
    usageLimit: dto.usageLimit,
    userUsageLimit: dto.userUsageLimit,
    startDate: new Date(dto.startDate),
    endDate: dto.endDate ? new Date(dto.endDate) : new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // Default 1 year
    isActive: dto.isActive,
    applicableProducts: dto.applicableProducts || [],
    applicableCategories: dto.applicableCategories || [],
    excludedProducts: dto.excludedProducts || [],
    excludedCategories: dto.excludedCategories || [],
    createdBy,
    metadata: {},
  }) as Omit<PromotionEntity, 'id' | 'createdAt' | 'updatedAt' | 'usageCount' | 'status'>;
}

/**
 * Transform UpdatePromotionDto to partial PromotionEntity data
 */
export function updatePromotionDtoToEntity(dto: UpdatePromotionDto): Partial<PromotionEntity> {
  return removeUndefined({
    name: dto.name,
    description: dto.description,
    value: dto.value,
    isPercentage: dto.type === PromotionType.PERCENTAGE,
    minOrderValue: dto.minOrderAmount,
    maxDiscountValue: dto.maxDiscountAmount,
    usageLimit: dto.usageLimit,
    userUsageLimit: dto.userUsageLimit,
    startDate: dto.startDate ? new Date(dto.startDate) : undefined,
    endDate: dto.endDate ? new Date(dto.endDate) : undefined,
    isActive: dto.isActive,
    applicableProducts: dto.applicableProducts,
    applicableCategories: dto.applicableCategories,
    excludedProducts: dto.excludedProducts,
    excludedCategories: dto.excludedCategories,
  });
}

/**
 * Transform array of PromotionEntity to array of PromotionResponseDto
 */
export function promotionsToResponseDtos(promotions: PromotionEntity[]): PromotionResponseDto[] {
  return promotions.map(promotionToResponseDto);
}

/**
 * Transform array of PromotionEntity to array of PromotionDto
 */
export function promotionsToDtos(promotions: PromotionEntity[]): PromotionDto[] {
  return promotions.map(promotionToDto);
}

/**
 * Helper function to get promotion status
 */
function getPromotionStatus(promotion: PromotionEntity): PromotionStatus {
  const now = new Date();

  if (!promotion.isActive) {
    return PromotionStatus.PAUSED;
  }

  if (now < promotion.startDate) {
    return PromotionStatus.DRAFT;
  }

  if (promotion.endDate && now > promotion.endDate) {
    return PromotionStatus.EXPIRED;
  }

  if (promotion.usageLimit && promotion.usageCount >= promotion.usageLimit) {
    return PromotionStatus.EXPIRED;
  }

  return PromotionStatus.ACTIVE;
}

/**
 * Helper function to check if promotion can be used
 */
function canUsePromotion(promotion: PromotionEntity): boolean {
  const now = new Date();
  
  return (
    promotion.isActive &&
    now >= promotion.startDate &&
    (!promotion.endDate || now <= promotion.endDate) &&
    (!promotion.usageLimit || promotion.usageCount < promotion.usageLimit)
  );
}

/**
 * Helper function to format discount display
 */
function formatDiscountDisplay(promotion: PromotionEntity): string {
  switch (promotion.type) {
    case PromotionType.PERCENTAGE:
      return `${promotion.value}%`;
    case PromotionType.FIXED_AMOUNT:
      return `${promotion.value.toLocaleString('vi-VN')}đ`;
    case PromotionType.FREE_SHIPPING:
      return "Miễn phí ship";
    case PromotionType.BUY_X_GET_Y:
      return `Mua ${promotion.value} tặng 1`;
    default:
      return promotion.value.toString();
  }
}

/**
 * Transform Prisma promotion to PromotionEntity
 */
export function prismaPromotionToEntity(prismaPromotion: any): PromotionEntity {
  return {
    id: prismaPromotion.id,
    name: prismaPromotion.name,
    description: prismaPromotion.description,
    type: prismaPromotion.type,
    value: prismaPromotion.value,
    isPercentage: prismaPromotion.type === PromotionType.PERCENTAGE,
    minOrderValue: prismaPromotion.minOrderAmount,
    maxDiscountValue: prismaPromotion.maxDiscountAmount,
    usageLimit: prismaPromotion.usageLimit,
    usageCount: prismaPromotion.usageCount,
    userUsageLimit: prismaPromotion.userUsageLimit,
    startDate: prismaPromotion.startDate,
    endDate: prismaPromotion.endDate,
    isActive: prismaPromotion.isActive,
    status: getPromotionStatus(prismaPromotion),
    code: prismaPromotion.code,
    applicableProducts: prismaPromotion.applicableProducts || [],
    excludedProducts: prismaPromotion.excludedProducts || [],
    applicableCategories: prismaPromotion.applicableCategories || [],
    excludedCategories: prismaPromotion.excludedCategories || [],
    createdBy: prismaPromotion.createdBy,
    metadata: prismaPromotion.metadata || {},
    createdAt: prismaPromotion.createdAt,
    updatedAt: prismaPromotion.updatedAt,
  };
}

/**
 * Transform PromotionEntity to Prisma data
 */
export function promotionEntityToPrisma(promotion: Partial<PromotionEntity>): any {
  return removeUndefined({
    name: promotion.name,
    description: promotion.description,
    code: promotion.code,
    type: promotion.type,
    value: promotion.value,
    minOrderAmount: promotion.minOrderValue,
    maxDiscountAmount: promotion.maxDiscountValue,
    usageLimit: promotion.usageLimit,
    usageCount: promotion.usageCount,
    userUsageLimit: promotion.userUsageLimit,
    startDate: promotion.startDate,
    endDate: promotion.endDate,
    isActive: promotion.isActive,
    applicableProducts: promotion.applicableProducts || [],
    applicableCategories: promotion.applicableCategories || [],
    excludedProducts: promotion.excludedProducts || [],
    excludedCategories: promotion.excludedCategories || [],
    createdBy: promotion.createdBy,
  });
}