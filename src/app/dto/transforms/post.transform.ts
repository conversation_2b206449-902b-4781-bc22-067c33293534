/**
 * Post Transform Functions
 * <PERSON>yển đổi giữa Prisma entities và DTOs
 */

import { Post, AdminUser, Category, Media } from "@prisma/client";
import {
  PostDto,
  PostWithRelationsDto,
  PostListItemDto,
  CreatePostDto,
  UpdatePostDto,
} from "../post.dto";
import { PostEntity, CreatePostData, UpdatePostData } from "../../models/post.model";

type PostWithRelations = Post & {
  author?: AdminUser;
  category?: Category;
  featuredImage?: Media;
};

/**
 * Transform Prisma Post to PostDto
 */
export function transformPostToDto(post: Post): PostDto {
  return {
    id: post.id,
    title: post.title,
    content: post.content,
    excerpt: post.excerpt || undefined,
    slug: post.slug,
    status: post.status,
    featured: post.featured,
    featuredImageId: post.featuredImageId || undefined,
    tags: post.tags,
    categoryId: post.categoryId || undefined,
    authorId: post.authorId,
    viewCount: post.viewCount,
    commentCount: post.commentCount,
    publishedAt: post.publishedAt?.toISOString(),
    metaTitle: post.metaTitle || undefined,
    metaDescription: post.metaDescription || undefined,
    keywords: post.keywords,
    metadata: post.metadata as Record<string, any> || undefined,
    createdAt: post.createdAt.toISOString(),
    updatedAt: post.updatedAt.toISOString(),
  };
}

/**
 * Transform Prisma Post with relations to PostWithRelationsDto
 */
export function transformPostWithRelationsToDto(post: PostWithRelations): PostWithRelationsDto {
  const baseDto = transformPostToDto(post);
  
  return {
    ...baseDto,
    author: post.author ? {
      id: post.author.id,
      name: post.author.name,
      email: post.author.email,
      avatarId: post.author.avatarId || undefined,
    } : undefined,
    category: post.category ? {
      id: post.category.id,
      name: post.category.name,
      slug: post.category.slug,
    } : undefined,
    featuredImage: post.featuredImage ? {
      id: post.featuredImage.id,
      url: post.featuredImage.url,
      alt: post.featuredImage.alt || undefined,
    } : undefined,
  };
}

/**
 * Transform Prisma Post to PostListItemDto
 */
export function transformPostToListItemDto(post: PostWithRelations): PostListItemDto {
  return {
    id: post.id,
    title: post.title,
    excerpt: post.excerpt || undefined,
    slug: post.slug,
    status: post.status,
    featured: post.featured,
    featuredImageUrl: post.featuredImage?.url,
    tags: post.tags,
    viewCount: post.viewCount,
    commentCount: post.commentCount,
    publishedAt: post.publishedAt?.toISOString(),
    createdAt: post.createdAt.toISOString(),
    updatedAt: post.updatedAt.toISOString(),
    author: {
      id: post.author?.id || post.authorId,
      name: post.author?.name || 'Unknown Author',
    },
    category: post.category ? {
      id: post.category.id,
      name: post.category.name,
    } : undefined,
  };
}

/**
 * Transform CreatePostDto to CreatePostData (for business layer)
 */
export function transformCreatePostDtoToData(dto: CreatePostDto, authorId: string): CreatePostData {
  return {
    title: dto.title,
    slug: dto.slug,
    content: dto.content,
    excerpt: dto.excerpt,
    status: dto.status,
    authorId,
    categoryId: dto.categoryId,
    featuredImageId: dto.featuredImageId,
    publishedAt: dto.publishedAt ? new Date(dto.publishedAt) : undefined,
    tags: dto.tags,
    featured: dto.featured,
    metaTitle: dto.metaTitle,
    metaDescription: dto.metaDescription,
    keywords: dto.keywords,
    metadata: dto.metadata,
  };
}

/**
 * Transform UpdatePostDto to UpdatePostData (for business layer)
 */
export function transformUpdatePostDtoToData(dto: UpdatePostDto): UpdatePostData {
  return {
    title: dto.title,
    slug: dto.slug,
    content: dto.content,
    excerpt: dto.excerpt,
    status: dto.status,
    categoryId: dto.categoryId,
    featuredImageId: dto.featuredImageId,
    publishedAt: dto.publishedAt ? new Date(dto.publishedAt) : undefined,
    tags: dto.tags,
    featured: dto.featured,
    metaTitle: dto.metaTitle,
    metaDescription: dto.metaDescription,
    keywords: dto.keywords,
    metadata: dto.metadata,
  };
}

/**
 * Transform PostEntity to PostDto
 */
export function transformPostEntityToDto(entity: PostEntity): PostDto {
  return {
    id: entity.id,
    title: entity.title,
    content: entity.content,
    excerpt: entity.excerpt,
    slug: entity.slug,
    status: entity.status,
    featured: entity.featured,
    featuredImageId: entity.featuredImageId,
    tags: entity.tags,
    categoryId: entity.categoryId,
    authorId: entity.authorId,
    viewCount: entity.viewCount,
    commentCount: entity.commentCount,
    publishedAt: entity.publishedAt?.toISOString(),
    metaTitle: entity.metaTitle,
    metaDescription: entity.metaDescription,
    keywords: entity.keywords,
    metadata: entity.metadata,
    createdAt: entity.createdAt.toISOString(),
    updatedAt: entity.updatedAt.toISOString(),
  };
}