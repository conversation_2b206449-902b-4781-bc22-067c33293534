/**
 * Product Transform Functions
 * Chuyển đổi giữa Product entities và DTOs
 */

import {
  ProductEntity,
  ProductWithRelations,
  CreateProductData,
  UpdateProductData,
  ProductStats,
  ProductDimensions,
} from "../../models/product.model";
import { ProductStatus } from "../../models/common.model";
import {
  ProductResponseDto,
  ProductWithRelationsDto,
  CreateProductRequestDto,
  UpdateProductRequestDto,
  ProductStatsDto,
  ProductDimensionsDto,
} from "../product.dto";
import { ProductStatusDto } from "../common.dto";
import {
  toISOString,
  fromISOString,
  removeUndefined,
} from "./common.transform";
import { Product } from "@prisma/client";
import { ProductWithRelations as PrismaProductWithRelations } from "../../api/repositories/product.repository";

/**
 * Transform ProductEntity to ProductResponseDto
 */
export function productToResponseDto(
  product: ProductEntity
): ProductResponseDto {
  return removeUndefined({
    id: product.id,
    name: product.name,
    slug: product.slug,
    description: product.description,
    shortDescription: product.shortDescription,
    price: product.price,
    salePrice: product.salePrice,
    sku: product.sku,
    stock: product.stock,
    weight: product.weight,
    dimensions: product.dimensions
      ? productDimensionsToDto(product.dimensions)
      : undefined,
    status: transformProductStatus(product.status),
    featured: product.featured,
    tags: product.tags,
    categoryId: product.categoryId,
    brandId: product.brandId,
    createdAt: toISOString(product.createdAt)!,
    updatedAt: toISOString(product.updatedAt)!,
    seo: product.seo,
    metadata: product.metadata,
  }) as ProductResponseDto;
}

/**
 * Transform ProductWithRelations to ProductWithRelationsDto
 */
export function productWithRelationsToDto(
  product: ProductWithRelations
): ProductWithRelationsDto {
  return {
    ...productToResponseDto(product),
    category: product.category
      ? {
          id: product.category.id,
          name: product.category.name,
          slug: product.category.slug,
        }
      : undefined,
    brand: product.brand
      ? {
          id: product.brand.id,
          name: product.brand.name,
          slug: product.brand.slug,
          logo: product.brand.logo,
        }
      : undefined,
    media: product.media?.map((media) => ({
      id: media.id,
      url: media.url,
      alt: media.alt,
      isPrimary: media.isPrimary,
      order: media.order,
      thumbnailUrl: media.thumbnailUrl,
    })),
    reviews: product.reviews?.map((review) => ({
      id: review.id,
      rating: review.rating,
      comment: review.comment,
      userName: review.user?.name || "Anonymous",
      userAvatar: review.user?.avatar,
      createdAt: toISOString(review.createdAt)!,
    })),
    stats: product.stats ? productStatsToDto(product.stats) : undefined,
  };
}

/**
 * Transform CreateProductRequestDto to CreateProductData
 */
export function createProductRequestToData(
  dto: CreateProductRequestDto
): CreateProductData {
  return removeUndefined({
    name: dto.name,
    description: dto.description,
    shortDescription: dto.shortDescription,
    price: dto.price,
    salePrice: dto.salePrice,
    sku: dto.sku,
    stock: dto.stock,
    weight: dto.weight,
    dimensions: dto.dimensions
      ? productDimensionsFromDto(dto.dimensions)
      : undefined,
    categoryId: dto.categoryId,
    brandId: dto.brandId,
    featured: dto.featured,
    tags: dto.tags,
    status: dto.status ? transformProductStatusFromDto(dto.status) : undefined,
    seo: dto.seo,
    metadata: dto.metadata,
  }) as CreateProductData;
}

/**
 * Transform UpdateProductRequestDto to UpdateProductData
 */
export function updateProductRequestToData(
  dto: UpdateProductRequestDto
): UpdateProductData {
  return removeUndefined({
    name: dto.name,
    description: dto.description,
    shortDescription: dto.shortDescription,
    price: dto.price,
    salePrice: dto.salePrice,
    stock: dto.stock,
    weight: dto.weight,
    dimensions: dto.dimensions
      ? productDimensionsFromDto(dto.dimensions)
      : undefined,
    categoryId: dto.categoryId,
    brandId: dto.brandId,
    featured: dto.featured,
    tags: dto.tags,
    status: dto.status ? transformProductStatusFromDto(dto.status) : undefined,
    seo: dto.seo,
    metadata: dto.metadata,
  }) as UpdateProductData;
}

/**
 * Transform ProductStats to ProductStatsDto
 */
export function productStatsToDto(stats: ProductStats): ProductStatsDto {
  return {
    totalViews: stats.totalViews,
    totalSales: stats.totalSales,
    totalRevenue: stats.totalRevenue,
    averageRating: stats.averageRating,
    totalReviews: stats.totalReviews,
    conversionRate: stats.conversionRate,
    lastSoldAt: toISOString(stats.lastSoldAt),
  };
}

/**
 * Transform ProductDimensions to ProductDimensionsDto
 */
export function productDimensionsToDto(
  dimensions: ProductDimensions
): ProductDimensionsDto {
  return {
    length: dimensions.length,
    width: dimensions.width,
    height: dimensions.height,
    unit: dimensions.unit,
  };
}

/**
 * Transform ProductDimensionsDto to ProductDimensions
 */
export function productDimensionsFromDto(
  dto: ProductDimensionsDto
): ProductDimensions {
  return {
    length: dto.length,
    width: dto.width,
    height: dto.height,
    unit: dto.unit,
  };
}

/**
 * Transform ProductStatus to ProductStatusDto
 */
export function transformProductStatus(
  status: ProductStatus
): ProductStatusDto {
  switch (status) {
    case ProductStatus.ACTIVE:
      return ProductStatusDto.ACTIVE;
    case ProductStatus.INACTIVE:
      return ProductStatusDto.INACTIVE;
    case ProductStatus.OUT_OF_STOCK:
      return ProductStatusDto.OUT_OF_STOCK;
    default:
      return ProductStatusDto.ACTIVE;
  }
}

/**
 * Transform ProductStatusDto to ProductStatus
 */
export function transformProductStatusFromDto(
  status: ProductStatusDto
): ProductStatus {
  switch (status) {
    case ProductStatusDto.ACTIVE:
      return ProductStatus.ACTIVE;
    case ProductStatusDto.INACTIVE:
      return ProductStatus.INACTIVE;
    case ProductStatusDto.OUT_OF_STOCK:
      return ProductStatus.OUT_OF_STOCK;
    default:
      return ProductStatus.ACTIVE;
  }
}

/**
 * Transform array of products to DTOs
 */
export function productsToResponseDtos(
  products: ProductEntity[]
): ProductResponseDto[] {
  return products.map(productToResponseDto);
}

/**
 * Transform array of products with relations to DTOs
 */
export function productsWithRelationsToDtos(
  products: ProductWithRelations[]
): ProductWithRelationsDto[] {
  return products.map(productWithRelationsToDto);
}

/**
 * Transform product for public display (minimal data)
 */
export function productToPublicDto(product: ProductEntity): {
  id: string;
  name: string;
  slug: string;
  price: number;
  salePrice?: number;
  status: ProductStatusDto;
} {
  return {
    id: product.id,
    name: product.name,
    slug: product.slug,
    price: product.price,
    salePrice: product.salePrice,
    status: transformProductStatus(product.status),
  };
}

/**
 * Transform product search filters
 */
export function transformProductSearchFilters(params: Record<string, any>) {
  return removeUndefined({
    search: params.search,
    categoryId: params.categoryId,
    brandId: params.brandId,
    status: params.status
      ? transformProductStatusFromDto(params.status)
      : undefined,
    featured:
      params.featured !== undefined ? Boolean(params.featured) : undefined,
    priceMin: params.priceMin ? Number(params.priceMin) : undefined,
    priceMax: params.priceMax ? Number(params.priceMax) : undefined,
    inStock: params.inStock !== undefined ? Boolean(params.inStock) : undefined,
    tags: params.tags,
    attributes: params.attributes,
    rating: params.rating ? Number(params.rating) : undefined,
  });
}

/**
 * Transform Prisma Product to ProductEntity
 * Converts null values to undefined for optional fields
 */
export function transformPrismaProductToEntity(
  product: Product
): ProductEntity {
  return {
    id: product.id,
    name: product.name,
    slug: product.slug,
    description: product.description,
    shortDescription: undefined, // Not in Prisma schema
    price: product.price,
    salePrice: product.salePrice ?? undefined,
    sku: product.sku,
    stock: product.stock,
    weight: undefined, // Not in Prisma schema
    dimensions: undefined, // Not in Prisma schema
    status: product.status as ProductStatus,
    featured: product.featured,
    tags: product.tags,
    categoryId: product.categoryId,
    brandId: product.brandId ?? undefined,
    seo: undefined, // Not in Prisma schema
    metadata: undefined, // Not in Prisma schema
    createdAt: product.createdAt,
    updatedAt: product.updatedAt,
  };
}

/**
 * Transform array of Prisma Products to ProductEntities
 */
export function transformPrismaProductsToEntities(
  products: Product[]
): ProductEntity[] {
  return products.map(transformPrismaProductToEntity);
}

/**
 * Transform Prisma ProductWithRelations to ProductWithRelations
 * Converts null values to undefined for optional fields
 */
export function transformPrismaProductWithRelationsToEntity(
  product: PrismaProductWithRelations
): ProductWithRelations {
  return {
    ...transformPrismaProductToEntity(product),
    category: product.category,
    brand: product.brand,
    media: product.media,
    reviews: product.reviews,
    attributes: product.ProductAttribute?.map((attr) => ({
      attributeId: attr.attributeId,
      attributeName: attr.attribute.name,
      valueId: attr.attributeValueId,
      value: attr.attributeValue.value,
      displayOrder: 0, // Default value since it's not in Prisma schema
    })),
    inventoryEntries: product.inventoryEntries,
  };
}
