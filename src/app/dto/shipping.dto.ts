/**
 * Shipping DTOs
 * Data Transfer Objects for Shipping, synchronized with prisma.schema
 */

import { z } from 'zod';
import { ShippingMethodType } from '@prisma/client';

// --- Shipping Zone ---

export const ShippingZoneSchema = z.object({
  name: z.string().min(1, 'Tên khu vực là bắt buộc'),
  description: z.string().optional(),
  provinces: z.array(z.string()).min(1, 'Cần có ít nhất một tỉnh/thành'),
  isActive: z.boolean().default(true),
});

export const CreateShippingZoneRequestSchema = ShippingZoneSchema;
export const UpdateShippingZoneRequestSchema = ShippingZoneSchema.partial();

export type CreateShippingZoneRequestDto = z.infer<typeof CreateShippingZoneRequestSchema>;
export type UpdateShippingZoneRequestDto = z.infer<typeof UpdateShippingZoneRequestSchema>;

// --- Shipping Method ---

export const ShippingMethodSchema = z.object({
  zoneId: z.string().cuid('ID khu vực không hợp lệ'),
  name: z.string().min(1, 'Tên phương thức là bắt buộc'),
  description: z.string().optional(),
  type: z.nativeEnum(ShippingMethodType).default(ShippingMethodType.STANDARD),
  baseFee: z.number().min(0, 'Phí cơ bản không được âm'),
  freeShippingMin: z.number().min(0, 'Ngưỡng miễn phí vận chuyển không được âm').optional().nullable(),
  estimatedDays: z.string().min(1, 'Thời gian giao hàng ước tính là bắt buộc'),
  maxWeight: z.number().min(0).optional().nullable(),
  maxDimensions: z.object({
    length: z.number().min(0),
    width: z.number().min(0),
    height: z.number().min(0),
  }).optional().nullable(),
  isActive: z.boolean().default(true),
  sortOrder: z.number().int().default(0),
});

export const CreateShippingMethodRequestSchema = ShippingMethodSchema;
export const UpdateShippingMethodRequestSchema = ShippingMethodSchema.partial().omit({ zoneId: true });

export type CreateShippingMethodRequestDto = z.infer<typeof CreateShippingMethodRequestSchema>;
export type UpdateShippingMethodRequestDto = z.infer<typeof UpdateShippingMethodRequestSchema>;
