/**
 * Attribute DTOs
 * Data Transfer Objects cho Attribute
 */

import { z } from "zod";
import { AttributeType } from "../models/attribute.model";

/**
 * Attribute Response DTO
 */
export interface AttributeResponseDto {
  id: string;
  name: string;
  slug: string;
  description?: string;
  type: AttributeType;
  values?: string[];
  isRequired: boolean;
  isFilterable: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
  _count?: {
    values: number;
    products: number;
  };
}

/**
 * AttributeValue Response DTO
 */
export interface AttributeValueResponseDto {
  id: string;
  attributeId: string;
  value: string;
  slug: string;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
  _count?: {
    products: number;
  };
}

/**
 * Create Attribute Request DTO
 */
export interface CreateAttributeRequestDto {
  name: string;
  slug?: string;
  description?: string;
  type: AttributeType;
  values?: string[];
  isRequired?: boolean;
  isFilterable?: boolean;
  sortOrder?: number;
}

/**
 * Update Attribute Request DTO
 */
export interface UpdateAttributeRequestDto {
  name?: string;
  slug?: string;
  description?: string;
  type?: AttributeType;
  values?: string[];
  isRequired?: boolean;
  isFilterable?: boolean;
  sortOrder?: number;
}

/**
 * Create AttributeValue Request DTO
 */
export interface CreateAttributeValueRequestDto {
  value: string;
  slug?: string;
  sortOrder?: number;
}

/**
 * Update AttributeValue Request DTO
 */
export interface UpdateAttributeValueRequestDto {
  value?: string;
  slug?: string;
  sortOrder?: number;
}

/**
 * Validation Schemas
 */
export const CreateAttributeRequestSchema = z.object({
  name: z.string().min(1, "Attribute name is required").max(100, "Name too long"),
  slug: z.string().optional(),
  description: z.string().optional(),
  type: z.nativeEnum(AttributeType),
  values: z.array(z.string()).optional(),
  isRequired: z.boolean().default(false),
  isFilterable: z.boolean().default(true),
  sortOrder: z.number().int().min(0).default(0),
});

export const UpdateAttributeRequestSchema = CreateAttributeRequestSchema.partial();

export const CreateAttributeValueRequestSchema = z.object({
  value: z.string().min(1, "Value is required").max(100, "Value too long"),
  slug: z.string().optional(),
  sortOrder: z.number().int().min(0).default(0),
});

export const UpdateAttributeValueRequestSchema = CreateAttributeValueRequestSchema.partial();

/**
 * Validation functions
 */
export const validateCreateAttribute = (data: any): CreateAttributeRequestDto => {
  return CreateAttributeRequestSchema.parse(data);
};

export const validateUpdateAttribute = (data: any): Partial<CreateAttributeRequestDto> => {
  return UpdateAttributeRequestSchema.parse(data);
};

export const validateCreateAttributeValue = (data: any): CreateAttributeValueRequestDto => {
  return CreateAttributeValueRequestSchema.parse(data);
};

export const validateUpdateAttributeValue = (data: any): Partial<CreateAttributeValueRequestDto> => {
  return UpdateAttributeValueRequestSchema.parse(data);
};

/**
 * Type aliases for backward compatibility
 */
export type AttributeDto = AttributeResponseDto;
export type AttributeValueDto = AttributeValueResponseDto;
export type CreateAttributeDto = CreateAttributeRequestDto;
export type UpdateAttributeDto = UpdateAttributeRequestDto;
export type CreateAttributeValueDto = CreateAttributeValueRequestDto;
export type UpdateAttributeValueDto = UpdateAttributeValueRequestDto;