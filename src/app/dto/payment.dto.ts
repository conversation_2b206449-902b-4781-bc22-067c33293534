/**
 * Payment DTOs
 * Data Transfer Objects cho Payment modules
 */

import { z } from "zod";
import { 
  PaymentProviderDto, 
  PaymentMethodDto, 
  PaymentTransactionStatusDto 
} from "./common.dto";

/**
 * Payment Gateway DTOs
 */
export interface PaymentGatewayResponseDto {
  id: string;
  name: string;
  provider: PaymentProviderDto;
  isActive: boolean;
  isDefault: boolean;
  config: Record<string, any>;
  credentials: Record<string, any>;
  supportedMethods: string[];
  fees?: Record<string, any>;
  limits?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

export interface CreatePaymentGatewayRequestDto {
  name: string;
  provider: PaymentProviderDto;
  description?: string;
  config: Record<string, any>;
  credentials: Record<string, any>;
  supportedMethods: string[];
  fees?: Record<string, any>;
  limits?: Record<string, any>;
  isActive?: boolean;
  isDefault?: boolean;
}

export interface UpdatePaymentGatewayRequestDto {
  name?: string;
  description?: string;
  config?: Record<string, any>;
  credentials?: Record<string, any>;
  supportedMethods?: string[];
  fees?: Record<string, any>;
  limits?: Record<string, any>;
  isActive?: boolean;
  isDefault?: boolean;
}

/**
 * Payment Transaction DTOs
 */
export interface PaymentTransactionResponseDto {
  id: string;
  orderId?: string;
  gatewayId: string;
  externalId?: string;
  amount: number;
  currency: string;
  method: PaymentMethodDto;
  status: PaymentTransactionStatusDto;
  gatewayResponse?: Record<string, any>;
  failureReason?: string;
  processedAt?: string;
  refundedAt?: string;
  refundAmount?: number;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
  gateway?: PaymentGatewayResponseDto;
  order?: {
    id: string;
    orderNumber: string;
    total: number;
    user?: {
      id: string;
      name: string;
      email: string;
    };
  };
}

export interface CreatePaymentTransactionRequestDto {
  orderId?: string;
  gatewayId: string;
  amount: number;
  currency?: string;
  method: PaymentMethodDto;
  externalId?: string;
  metadata?: Record<string, any>;
}

export interface UpdatePaymentTransactionRequestDto {
  status?: PaymentTransactionStatusDto;
  gatewayResponse?: Record<string, any>;
  failureReason?: string;
  processedAt?: string;
  refundedAt?: string;
  refundAmount?: number;
  metadata?: Record<string, any>;
}

/**
 * Payment Processing DTOs
 */
export interface PaymentRequestDto {
  orderId: string;
  amount: number;
  currency?: string;
  method: PaymentMethodDto;
  gatewayId?: string;
  returnUrl?: string;
  cancelUrl?: string;
  metadata?: Record<string, any>;
}

export interface PaymentResponseDto {
  success: boolean;
  transactionId: string;
  status: PaymentTransactionStatusDto;
  paymentUrl?: string;
  message?: string;
  error?: string;
  gatewayResponse?: Record<string, any>;
}

export interface RefundRequestDto {
  transactionId: string;
  amount?: number; // If not provided, refund full amount
  reason?: string;
  metadata?: Record<string, any>;
}

export interface RefundResponseDto {
  success: boolean;
  refundId: string;
  refundAmount: number;
  status: PaymentTransactionStatusDto;
  message?: string;
  error?: string;
}

/**
 * Validation Schemas
 */
export const CreatePaymentGatewayRequestSchema = z.object({
  name: z.string().min(1, "Gateway name is required"),
  provider: z.nativeEnum(PaymentProviderDto),
  description: z.string().optional(),
  config: z.record(z.any()),
  credentials: z.record(z.any()),
  supportedMethods: z.array(z.string()).min(1, "At least one supported method is required"),
  fees: z.record(z.any()).optional(),
  limits: z.record(z.any()).optional(),
  isActive: z.boolean().default(true),
  isDefault: z.boolean().default(false),
});

export const UpdatePaymentGatewayRequestSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  config: z.record(z.any()).optional(),
  credentials: z.record(z.any()).optional(),
  supportedMethods: z.array(z.string()).optional(),
  fees: z.record(z.any()).optional(),
  limits: z.record(z.any()).optional(),
  isActive: z.boolean().optional(),
  isDefault: z.boolean().optional(),
});

export const CreatePaymentTransactionRequestSchema = z.object({
  orderId: z.string().uuid().optional(),
  gatewayId: z.string().uuid("Invalid gateway ID"),
  amount: z.number().positive("Amount must be positive"),
  currency: z.string().default("VND"),
  method: z.nativeEnum(PaymentMethodDto),
  externalId: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

export const UpdatePaymentTransactionRequestSchema = z.object({
  status: z.nativeEnum(PaymentTransactionStatusDto).optional(),
  gatewayResponse: z.record(z.any()).optional(),
  failureReason: z.string().optional(),
  processedAt: z.string().datetime().optional(),
  refundedAt: z.string().datetime().optional(),
  refundAmount: z.number().optional(),
  metadata: z.record(z.any()).optional(),
});

export const PaymentRequestSchema = z.object({
  orderId: z.string().uuid("Invalid order ID"),
  amount: z.number().positive("Amount must be positive"),
  currency: z.string().default("VND"),
  method: z.nativeEnum(PaymentMethodDto),
  gatewayId: z.string().uuid().optional(),
  returnUrl: z.string().url().optional(),
  cancelUrl: z.string().url().optional(),
  metadata: z.record(z.any()).optional(),
});

export const RefundRequestSchema = z.object({
  transactionId: z.string().uuid("Invalid transaction ID"),
  amount: z.number().positive().optional(),
  reason: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * Validation functions
 */
export const validateCreatePaymentGateway = (data: any): CreatePaymentGatewayRequestDto => {
  return CreatePaymentGatewayRequestSchema.parse(data);
};

export const validateUpdatePaymentGateway = (data: any): UpdatePaymentGatewayRequestDto => {
  return UpdatePaymentGatewayRequestSchema.parse(data);
};

export const validateCreatePaymentTransaction = (data: any): CreatePaymentTransactionRequestDto => {
  return CreatePaymentTransactionRequestSchema.parse(data);
};

export const validateUpdatePaymentTransaction = (data: any): UpdatePaymentTransactionRequestDto => {
  return UpdatePaymentTransactionRequestSchema.parse(data);
};

export const validatePaymentRequest = (data: any): PaymentRequestDto => {
  return PaymentRequestSchema.parse(data);
};

export const validateRefundRequest = (data: any): RefundRequestDto => {
  return RefundRequestSchema.parse(data);
};

/**
 * Type aliases for backward compatibility
 */
export type PaymentGatewayDto = PaymentGatewayResponseDto;
export type CreatePaymentGatewayDto = CreatePaymentGatewayRequestDto;
export type UpdatePaymentGatewayDto = UpdatePaymentGatewayRequestDto;

export type PaymentTransactionDto = PaymentTransactionResponseDto;
export type CreatePaymentTransactionDto = CreatePaymentTransactionRequestDto;
export type UpdatePaymentTransactionDto = UpdatePaymentTransactionRequestDto;