/**
 * Shipping Models
 * Business entities for Shipping, synchronized with prisma.schema
 */

import { BaseEntity } from "./common.model";
import { ShippingMethodType } from "@prisma/client";

// --- Shipping Zone ---

/**
 * Shipping Zone Entity
 * Represents a geographical area for shipping.
 */
export interface ShippingZoneEntity extends BaseEntity {
  name: string;
  description?: string | null;
  provinces: string[];
  isActive: boolean;
  methods?: ShippingMethodEntity[]; // Relation
}

/**
 * Data for creating a new Shipping Zone.
 */
export interface CreateShippingZoneData {
  name: string;
  description?: string;
  provinces: string[];
  isActive?: boolean;
}

/**
 * Data for updating a Shipping Zone.
 */
export type UpdateShippingZoneData = Partial<CreateShippingZoneData>;


// --- Shipping Method ---

/**
 * Shipping Method Entity
 * Represents a shipping option within a zone.
 */
export interface ShippingMethodEntity extends BaseEntity {
  zoneId: string;
  name: string;
  description?: string | null;
  type: ShippingMethodType;
  baseFee: number;
  freeShippingMin?: number | null;
  estimatedDays: string;
  maxWeight?: number | null;
  maxDimensions?: { length: number; width: number; height: number; } | null;
  isActive: boolean;
  sortOrder: number;
}

/**
 * Data for creating a new Shipping Method.
 */
export interface CreateShippingMethodData {
  zoneId: string;
  name: string;
  description?: string;
  type?: ShippingMethodType;
  baseFee: number;
  freeShippingMin?: number;
  estimatedDays: string;
  maxWeight?: number;
  maxDimensions?: { length: number; width: number; height: number; };
  isActive?: boolean;
  sortOrder?: number;
}

/**
 * Data for updating a Shipping Method.
 */
export type UpdateShippingMethodData = Partial<CreateShippingMethodData>;

/**
 * Business Rules for Shipping
 */
export class ShippingBusinessRules {
  static validateZone(data: CreateShippingZoneData | UpdateShippingZoneData): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    if (!data.name?.trim()) {
      errors.push("Tên khu vực là bắt buộc.");
    }
    if (!data.provinces || data.provinces.length === 0) {
      errors.push("Cần có ít nhất một tỉnh/thành trong khu vực.");
    }
    return { valid: errors.length === 0, errors };
  }

  static validateMethod(data: CreateShippingMethodData | UpdateShippingMethodData): { valid: boolean; errors: string[] } {
    const errors: string[] = [];
    if (!data.name?.trim()) {
      errors.push("Tên phương thức là bắt buộc.");
    }
    if (data.baseFee === undefined || data.baseFee < 0) {
      errors.push("Phí cơ bản phải là một số không âm.");
    }
    if (!data.estimatedDays?.trim()) {
      errors.push("Thời gian giao hàng ước tính là bắt buộc.");
    }
    return { valid: errors.length === 0, errors };
  }
}
