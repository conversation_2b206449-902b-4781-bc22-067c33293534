/**
 * Address Model
 * Business entity cho Address, đồng bộ với prisma.schema
 */

import { BaseEntity } from "./common.model";

/**
 * Address Entity
 * Phản ánh cấu trúc trong schema.prisma
 */
export interface AddressEntity extends BaseEntity {
  userId: string;
  fullName: string;
  phone: string;
  address: string; // Street address
  ward: string;
  district: string;
  province: string;
  isDefault: boolean;
  metadata?: Record<string, any>;
}

/**
 * Create Address Data
 * Dữ liệu để tạo một địa chỉ mới
 */
export interface CreateAddressData {
  userId: string;
  fullName: string;
  phone: string;
  address: string;
  ward: string;
  district: string;
  province: string;
  isDefault?: boolean;
}

/**
 * Update Address Data
 * Dữ liệu để cập nhật một địa chỉ
 */
export type UpdateAddressData = Partial<CreateAddressData>;

/**
 * Address Business Rules
 */
export class AddressBusinessRules {
  static readonly MAX_ADDRESSES_PER_USER = 10;

  static validateAddress(data: CreateAddressData | UpdateAddressData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!data.fullName?.trim() || data.fullName.trim().length < 2) {
      errors.push("Họ và tên là bắt buộc và phải có ít nhất 2 ký tự.");
    }
    if (!data.phone?.trim() || !/^[0-9]{10,11}$/.test(data.phone.replace(/\s/g, ""))) {
      errors.push("Số điện thoại là bắt buộc và phải hợp lệ (10-11 số).");
    }
    if (!data.address?.trim() || data.address.trim().length < 5) {
      errors.push("Địa chỉ (số nhà, đường) là bắt buộc và phải có ít nhất 5 ký tự.");
    }
    if (!data.ward?.trim()) {
      errors.push("Phường/Xã là bắt buộc.");
    }
    if (!data.district?.trim()) {
      errors.push("Quận/Huyện là bắt buộc.");
    }
    if (!data.province?.trim()) {
      errors.push("Tỉnh/Thành phố là bắt buộc.");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static canAddMoreAddresses(currentCount: number): boolean {
    return currentCount < this.MAX_ADDRESSES_PER_USER;
  }

  static canDelete(
    address: AddressEntity,
    userAddresses: AddressEntity[]
  ): {
    canDelete: boolean;
    reason?: string;
  } {
    if (userAddresses.length <= 1) {
      return {
        canDelete: false,
        reason: "Không thể xóa địa chỉ duy nhất.",
      };
    }
    return { canDelete: true };
  }

  static formatFullAddress(address: AddressEntity): string {
    return [address.address, address.ward, address.district, address.province]
      .filter(Boolean)
      .join(", ");
  }
}
