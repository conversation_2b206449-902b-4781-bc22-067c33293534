/**
 * Attribute Model
 * Business entity cho Product Attributes
 */

import { BaseEntity } from "./common.model";

/**
 * Attribute Entity
 */
export interface AttributeEntity extends BaseEntity {
  name: string;
  slug: string;
  description?: string; // Maps from Prisma description field
  type: AttributeType;
  values: string[]; // Simplified values array (mapped from AttributeValue relations)
  isRequired: boolean;
  isFilterable: boolean;
  sortOrder: number;
  metadata?: Record<string, any>; // Optional, not in Prisma
}

/**
 * Attribute Type Enum
 */
export enum AttributeType {
  TEXT = "TEXT",
  NUMBER = "NUMBER",
  SELECT = "SELECT",
  MULTI_SELECT = "MULTI_SELECT",
  BOOLEAN = "BOOLEAN",
  COLOR = "COLOR",
  SIZE = "SIZE",
}

/**
 * Create Attribute Data
 */
export interface CreateAttributeData {
  name: string;
  slug?: string;
  description?: string;
  type: AttributeType;
  values?: string[];
  isRequired?: boolean;
  isFilterable?: boolean;
  sortOrder?: number;
  metadata?: Record<string, any>;
}

/**
 * Update Attribute Data
 */
export interface UpdateAttributeData {
  name?: string;
  slug?: string;
  description?: string;
  type?: AttributeType;
  values?: string[];
  isRequired?: boolean;
  isFilterable?: boolean;
  sortOrder?: number;
  metadata?: Record<string, any>;
}

/**
 * Attribute Search Filters
 */
export interface AttributeSearchFilters {
  search?: string;
  type?: AttributeType;
  isRequired?: boolean;
  isFilterable?: boolean;
  page?: number;
  limit?: number;
}

/**
 * Attribute Business Rules
 */
export class AttributeBusinessRules {
  static readonly MAX_NAME_LENGTH = 100;
  static readonly MAX_VALUES_COUNT = 50;
  static readonly MAX_VALUE_LENGTH = 100;

  static validateName(name: string): boolean {
    return name.length > 0 && name.length <= this.MAX_NAME_LENGTH;
  }

  static validateValues(values: string[]): boolean {
    if (values.length > this.MAX_VALUES_COUNT) return false;
    return values.every(
      (value) => value.length > 0 && value.length <= this.MAX_VALUE_LENGTH
    );
  }

  static generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/^-+|-+$/g, "");
  }

  static canDelete(attribute: AttributeEntity): boolean {
    // Add business logic for deletion rules
    // For example, check if attribute is used by products
    return true;
  }

  static validateAttributeData(
    data: CreateAttributeData | UpdateAttributeData
  ): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if ("name" in data && data.name && !this.validateName(data.name)) {
      errors.push(
        `Name must be between 1 and ${this.MAX_NAME_LENGTH} characters`
      );
    }

    if ("values" in data && data.values && !this.validateValues(data.values)) {
      errors.push(`Values validation failed`);
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}
