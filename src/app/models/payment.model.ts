/**
 * Payment Models
 * Business entities cho Payment modules
 */

import { 
  BaseEntity, 
  PaymentProvider, 
  PaymentMethod, 
  PaymentTransactionStatus 
} from "./common.model";

/**
 * Payment Gateway Entity
 */
export interface PaymentGatewayEntity extends BaseEntity {
  name: string;
  provider: PaymentProvider;
  isActive: boolean;
  isDefault: boolean;
  config: PaymentGatewayConfig;
  credentials: PaymentGatewayCredentials;
  supportedMethods: string[];
  fees?: PaymentGatewayFees;
  limits?: PaymentGatewayLimits;
}

/**
 * Payment Gateway Configuration
 */
export interface PaymentGatewayConfig {
  apiVersion?: string;
  environment?: "sandbox" | "production";
  webhookUrl?: string;
  returnUrl?: string;
  cancelUrl?: string;
  currency?: string;
  timeout?: number;
  retryAttempts?: number;
  [key: string]: any;
}

/**
 * Payment Gateway Credentials
 */
export interface PaymentGatewayCredentials {
  apiKey?: string;
  secretKey?: string;
  merchantId?: string;
  publicKey?: string;
  clientId?: string;
  clientSecret?: string;
  [key: string]: any;
}

/**
 * Payment Gateway Fees
 */
export interface PaymentGatewayFees {
  percentage?: number;
  fixedAmount?: number;
  minimumFee?: number;
  maximumFee?: number;
  currency?: string;
}

/**
 * Payment Gateway Limits
 */
export interface PaymentGatewayLimits {
  minAmount?: number;
  maxAmount?: number;
  dailyLimit?: number;
  monthlyLimit?: number;
  currency?: string;
}

/**
 * Payment Transaction Entity
 */
export interface PaymentTransactionEntity extends BaseEntity {
  orderId?: string;
  gatewayId: string;
  externalId?: string;
  amount: number;
  currency: string;
  method: PaymentMethod;
  status: PaymentTransactionStatus;
  gatewayResponse?: Record<string, any>;
  failureReason?: string;
  processedAt?: Date;
  refundedAt?: Date;
  refundAmount?: number;
  metadata?: Record<string, any>;
  
  // Relations
  gateway?: PaymentGatewayEntity;
  order?: any; // OrderEntity - avoiding circular dependency
}

/**
 * Payment Transaction with Relations
 */
export interface PaymentTransactionWithRelations extends PaymentTransactionEntity {
  gateway: PaymentGatewayEntity;
  order?: {
    id: string;
    orderNumber: string;
    total: number;
    user?: {
      id: string;
      name: string;
      email: string;
    };
  };
}

/**
 * Payment Request
 */
export interface PaymentRequest {
  orderId: string;
  amount: number;
  currency: string;
  method: PaymentMethod;
  gatewayId?: string;
  returnUrl?: string;
  cancelUrl?: string;
  metadata?: Record<string, any>;
}

/**
 * Payment Response
 */
export interface PaymentResponse {
  success: boolean;
  transactionId: string;
  status: PaymentTransactionStatus;
  paymentUrl?: string;
  message?: string;
  error?: string;
  gatewayResponse?: Record<string, any>;
}

/**
 * Refund Request
 */
export interface RefundRequest {
  transactionId: string;
  amount?: number;
  reason?: string;
  metadata?: Record<string, any>;
}

/**
 * Refund Response
 */
export interface RefundResponse {
  success: boolean;
  refundId: string;
  refundAmount: number;
  status: PaymentTransactionStatus;
  message?: string;
  error?: string;
}

/**
 * Payment Gateway Search Filters
 */
export interface PaymentGatewaySearchFilters {
  search?: string;
  provider?: PaymentProvider;
  isActive?: boolean;
  isDefault?: boolean;
  supportedMethod?: string;
}

/**
 * Payment Transaction Search Filters
 */
export interface PaymentTransactionSearchFilters {
  search?: string;
  orderId?: string;
  gatewayId?: string;
  status?: PaymentTransactionStatus;
  method?: PaymentMethod;
  dateFrom?: Date;
  dateTo?: Date;
  amountMin?: number;
  amountMax?: number;
}

/**
 * Payment Statistics
 */
export interface PaymentStats {
  totalTransactions: number;
  successfulTransactions: number;
  failedTransactions: number;
  pendingTransactions: number;
  totalAmount: number;
  successfulAmount: number;
  refundedAmount: number;
  averageTransactionAmount: number;
  successRate: number;
}

/**
 * Gateway Statistics
 */
export interface GatewayStats {
  gatewayId: string;
  gatewayName: string;
  provider: PaymentProvider;
  transactionCount: number;
  successCount: number;
  failedCount: number;
  totalAmount: number;
  successRate: number;
  averageAmount: number;
}

/**
 * Payment Business Rules
 */
export class PaymentBusinessRules {
  /**
   * Generate unique transaction ID
   */
  static generateTransactionId(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `TXN-${timestamp.slice(-8)}-${random}`;
  }

  /**
   * Validate payment gateway configuration
   */
  static validateGatewayConfig(
    provider: PaymentProvider,
    config: PaymentGatewayConfig,
    credentials: PaymentGatewayCredentials
  ): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Provider-specific validation
    switch (provider) {
      case PaymentProvider.VNPAY:
        if (!credentials.merchantId) errors.push("VNPay: Merchant ID is required");
        if (!credentials.secretKey) errors.push("VNPay: Secret key is required");
        break;

      case PaymentProvider.MOMO:
        if (!credentials.merchantId) errors.push("MoMo: Merchant ID is required");
        if (!credentials.secretKey) errors.push("MoMo: Secret key is required");
        break;

      case PaymentProvider.STRIPE:
        if (!credentials.publicKey) errors.push("Stripe: Public key is required");
        if (!credentials.secretKey) errors.push("Stripe: Secret key is required");
        break;

      case PaymentProvider.PAYPAL:
        if (!credentials.clientId) errors.push("PayPal: Client ID is required");
        if (!credentials.clientSecret) errors.push("PayPal: Client secret is required");
        break;
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Check if transaction can be refunded
   */
  static canRefund(transaction: PaymentTransactionEntity): boolean {
    return (
      transaction.status === PaymentTransactionStatus.SUCCESS &&
      !transaction.refundedAt &&
      transaction.processedAt &&
      new Date().getTime() - new Date(transaction.processedAt).getTime() < 180 * 24 * 60 * 60 * 1000 // 180 days
    );
  }

  /**
   * Calculate refundable amount
   */
  static getRefundableAmount(transaction: PaymentTransactionEntity): number {
    if (!this.canRefund(transaction)) return 0;

    const refundedAmount = transaction.refundAmount || 0;
    return transaction.amount - refundedAmount;
  }

  /**
   * Validate payment amount
   */
  static validateAmount(
    amount: number,
    gateway: PaymentGatewayEntity
  ): { valid: boolean; error?: string } {
    if (amount <= 0) {
      return { valid: false, error: "Amount must be greater than 0" };
    }

    if (gateway.limits?.minAmount && amount < gateway.limits.minAmount) {
      return { 
        valid: false, 
        error: `Amount must be at least ${gateway.limits.minAmount} ${gateway.limits.currency || 'VND'}` 
      };
    }

    if (gateway.limits?.maxAmount && amount > gateway.limits.maxAmount) {
      return { 
        valid: false, 
        error: `Amount cannot exceed ${gateway.limits.maxAmount} ${gateway.limits.currency || 'VND'}` 
      };
    }

    return { valid: true };
  }

  /**
   * Calculate transaction fees
   */
  static calculateFees(
    amount: number,
    gateway: PaymentGatewayEntity
  ): number {
    if (!gateway.fees) return 0;

    let fee = 0;

    // Percentage fee
    if (gateway.fees.percentage) {
      fee += amount * (gateway.fees.percentage / 100);
    }

    // Fixed fee
    if (gateway.fees.fixedAmount) {
      fee += gateway.fees.fixedAmount;
    }

    // Apply limits
    if (gateway.fees.minimumFee && fee < gateway.fees.minimumFee) {
      fee = gateway.fees.minimumFee;
    }

    if (gateway.fees.maximumFee && fee > gateway.fees.maximumFee) {
      fee = gateway.fees.maximumFee;
    }

    return Math.round(fee);
  }

  /**
   * Get supported payment methods for a gateway
   */
  static getSupportedMethods(gateway: PaymentGatewayEntity): PaymentMethod[] {
    return gateway.supportedMethods
      .filter(method => Object.values(PaymentMethod).includes(method as PaymentMethod))
      .map(method => method as PaymentMethod);
  }

  /**
   * Check if payment method is supported by gateway
   */
  static isMethodSupported(
    method: PaymentMethod,
    gateway: PaymentGatewayEntity
  ): boolean {
    return gateway.supportedMethods.includes(method);
  }

  /**
   * Get next transaction status based on current status
   */
  static getNextStatus(currentStatus: PaymentTransactionStatus): PaymentTransactionStatus[] {
    const statusFlow: Record<PaymentTransactionStatus, PaymentTransactionStatus[]> = {
      [PaymentTransactionStatus.PENDING]: [
        PaymentTransactionStatus.PROCESSING,
        PaymentTransactionStatus.CANCELLED,
        PaymentTransactionStatus.FAILED,
      ],
      [PaymentTransactionStatus.PROCESSING]: [
        PaymentTransactionStatus.SUCCESS,
        PaymentTransactionStatus.FAILED,
      ],
      [PaymentTransactionStatus.SUCCESS]: [
        PaymentTransactionStatus.REFUNDED,
        PaymentTransactionStatus.PARTIAL_REFUND,
      ],
      [PaymentTransactionStatus.FAILED]: [],
      [PaymentTransactionStatus.CANCELLED]: [],
      [PaymentTransactionStatus.REFUNDED]: [],
      [PaymentTransactionStatus.PARTIAL_REFUND]: [
        PaymentTransactionStatus.REFUNDED,
      ],
    };

    return statusFlow[currentStatus] || [];
  }

  /**
   * Check if status transition is allowed
   */
  static canTransitionTo(
    currentStatus: PaymentTransactionStatus,
    newStatus: PaymentTransactionStatus
  ): boolean {
    const allowedTransitions = this.getNextStatus(currentStatus);
    return allowedTransitions.includes(newStatus);
  }
}