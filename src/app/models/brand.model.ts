/**
 * Brand Model
 * Business entity cho Brand
 */

import { BaseEntity, Status, SEOData } from "./common.model";

/**
 * Brand Entity
 */
export interface BrandEntity extends BaseEntity {
  name: string;
  slug: string;
  description?: string;
  logo?: string; // logoId reference
  website?: string;
  status: Status; // Maps from Prisma isActive boolean
  seo?: SEOData; // Optional, not in Prisma
  metadata?: Record<string, any>; // Optional, not in Prisma
}

/**
 * Brand with Relations
 */
export interface BrandWithRelations extends BrandEntity {
  products?: any[]; // ProductEntity[]
  stats?: BrandStats;
}

/**
 * Brand Statistics
 */
export interface BrandStats {
  totalProducts: number;
  activeProducts: number;
  totalSales: number;
  totalRevenue: number;
}

/**
 * Brand Creation Data
 */
export interface CreateBrandData {
  name: string;
  description?: string;
  logo?: string;
  website?: string;
  status?: Status;
  seo?: SEOData;
  metadata?: Record<string, any>;
}

/**
 * Brand Update Data
 */
export interface UpdateBrandData {
  name?: string;
  slug?: string;
  description?: string;
  logo?: string;
  website?: string;
  status?: Status;
  seo?: SEOData;
  metadata?: Record<string, any>;
}

/**
 * Brand Business Rules
 */
export class BrandBusinessRules {
  static generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();
  }

  static validateSlug(slug: string): boolean {
    const slugRegex = /^[a-z0-9-]+$/;
    return slugRegex.test(slug) && slug.length >= 2 && slug.length <= 100;
  }

  static validateWebsite(website: string): boolean {
    try {
      new URL(website);
      return true;
    } catch {
      return false;
    }
  }

  static validateBrand(data: CreateBrandData | UpdateBrandData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validate name for create
    if ("name" in data && data.name !== undefined) {
      if (!data.name?.trim()) {
        errors.push("Brand name is required");
      } else if (data.name.trim().length < 2) {
        errors.push("Brand name must be at least 2 characters");
      } else if (data.name.trim().length > 100) {
        errors.push("Brand name must be less than 100 characters");
      }
    }

    // Validate website if provided
    if (data.website && !this.validateWebsite(data.website)) {
      errors.push("Invalid website URL format");
    }

    // Validate description length
    if (data.description && data.description.length > 1000) {
      errors.push("Description must be less than 1000 characters");
    }

    // Validate status
    if ("status" in data && data.status !== undefined) {
      if (!Object.values(Status).includes(data.status)) {
        errors.push("Invalid status value");
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static canDelete(
    brand: BrandEntity,
    productCount: number
  ): {
    canDelete: boolean;
    reason?: string;
  } {
    if (productCount > 0) {
      return {
        canDelete: false,
        reason: `Brand has ${productCount} product(s) associated with it`,
      };
    }

    return { canDelete: true };
  }

  static getStatusLabel(status: Status): string {
    switch (status) {
      case Status.ACTIVE:
        return "Active";
      case Status.INACTIVE:
        return "Inactive";
      case Status.DRAFT:
        return "Draft";
      case Status.ARCHIVED:
        return "Archived";
      default:
        return "Unknown";
    }
  }

  static isValidLogoUrl(url: string): boolean {
    if (!url) return true; // Logo is optional

    try {
      new URL(url);
      // Check if it's an image URL (basic check)
      const imageExtensions = [
        ".jpg",
        ".jpeg",
        ".png",
        ".gif",
        ".webp",
        ".svg",
      ];
      return imageExtensions.some((ext) => url.toLowerCase().includes(ext));
    } catch {
      return false;
    }
  }

  static generateSEOTitle(name: string): string {
    return `${name} - Premium Fashion Brand | NS Shop`;
  }

  static generateSEODescription(name: string, description?: string): string {
    if (description) {
      return description.length > 160
        ? description.substring(0, 157) + "..."
        : description;
    }
    return `Discover ${name} collection at NS Shop. Premium fashion and quality products.`;
  }

  static calculateBrandScore(stats: BrandStats): number {
    // Simple scoring algorithm based on products and sales
    const productScore = Math.min(stats.activeProducts * 2, 50);
    const salesScore = Math.min(stats.totalSales / 10, 30);
    const revenueScore = Math.min(stats.totalRevenue / 1000, 20);

    return Math.round(productScore + salesScore + revenueScore);
  }
}
