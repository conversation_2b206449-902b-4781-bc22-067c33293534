/**
 * Product Model
 * Business entity cho Product
 */

import { BaseEntity, ProductStatus, SEOData } from "./common.model";

/**
 * Product Entity
 */
export interface ProductEntity extends BaseEntity {
  name: string;
  slug: string;
  description: string;
  shortDescription?: string;
  price: number;
  salePrice?: number;
  sku: string;
  stock: number;
  weight?: number;
  dimensions?: ProductDimensions;
  status: ProductStatus;
  featured: boolean;
  tags: string[];
  categoryId: string;
  brandId?: string;
  seo?: SEOData;
  metadata?: Record<string, any>;
}

/**
 * Product Dimensions
 */
export interface ProductDimensions {
  length: number;
  width: number;
  height: number;
  unit: "cm" | "inch";
}

/**
 * Product with Relations
 */
export interface ProductWithRelations extends ProductEntity {
  category?: any; // CategoryEntity
  brand?: any; // BrandEntity
  media?: any[]; // ProductMediaEntity[]
  reviews?: any[]; // ReviewEntity[]
  attributes?: ProductAttributeValue[];
  inventoryEntries?: any[]; // InventoryEntity[]
  variants?: ProductVariant[];
  stats?: ProductStats;
}

/**
 * Product Attribute Value
 */
export interface ProductAttributeValue {
  attributeId: string;
  attributeName: string;
  valueId: string;
  value: string;
  displayOrder: number;
}

/**
 * Product Variant
 */
export interface ProductVariant {
  id: string;
  name: string;
  sku: string;
  price: number;
  salePrice?: number;
  stock: number;
  attributes: ProductAttributeValue[];
  media?: any[]; // MediaEntity[]
}

/**
 * Product Statistics
 */
export interface ProductStats {
  totalViews: number;
  totalSales: number;
  totalRevenue: number;
  averageRating: number;
  totalReviews: number;
  conversionRate: number;
  lastSoldAt?: Date;
}

/**
 * Product Media
 */
export interface ProductMedia {
  id: string;
  productId: string;
  mediaId: string;
  isPrimary: boolean;
  order: number;
  alt?: string;
  media?: any; // MediaEntity
}

/**
 * Product Creation Data
 */
export interface CreateProductData {
  name: string;
  slug?: string;
  description: string;
  shortDescription?: string;
  price: number;
  salePrice?: number;
  sku: string;
  stock: number;
  weight?: number;
  dimensions?: ProductDimensions;
  categoryId: string;
  brandId?: string;
  featured?: boolean;
  tags?: string[];
  status?: ProductStatus;
  seo?: SEOData;
  metadata?: Record<string, any>;
  mediaIds?: string[];
  attributes?: Omit<ProductAttributeValue, "attributeName">[];
}

/**
 * Product Update Data
 */
export interface UpdateProductData {
  name?: string;
  slug?: string;
  description?: string;
  shortDescription?: string;
  price?: number;
  salePrice?: number;
  sku?: string;
  stock?: number;
  weight?: number;
  dimensions?: ProductDimensions;
  categoryId?: string;
  brandId?: string;
  featured?: boolean;
  tags?: string[];
  status?: ProductStatus;
  seo?: SEOData;
  metadata?: Record<string, any>;
}

/**
 * Product Search Filters
 */
export interface ProductSearchFilters {
  search?: string;
  categoryId?: string;
  brandId?: string;
  status?: ProductStatus;
  featured?: boolean;
  priceMin?: number;
  priceMax?: number;
  inStock?: boolean;
  tags?: string[];
  attributes?: Record<string, string[]>;
  rating?: number;
}

/**
 * Product Sort Options
 */
export type ProductSortBy =
  | "name"
  | "price"
  | "createdAt"
  | "updatedAt"
  | "rating"
  | "sales"
  | "views"
  | "stock";

/**
 * Product Inventory Update
 */
export interface ProductInventoryUpdate {
  productId: string;
  quantity: number;
  type: "INCREASE" | "DECREASE" | "SET";
  reason?: string;
  reference?: string;
}

/**
 * Product Price History
 */
export interface ProductPriceHistory {
  productId: string;
  oldPrice: number;
  newPrice: number;
  oldSalePrice?: number;
  newSalePrice?: number;
  changedBy: string;
  reason?: string;
  timestamp: Date;
}

/**
 * Product Business Rules
 */
export class ProductBusinessRules {
  static validateSKU(sku: string): boolean {
    // SKU should be alphanumeric and can contain hyphens/underscores
    const skuRegex = /^[A-Za-z0-9_-]+$/;
    return skuRegex.test(sku) && sku.length >= 3 && sku.length <= 50;
  }

  static validatePrice(price: number): boolean {
    return price > 0;
  }

  static validateSalePrice(regularPrice: number, salePrice: number): boolean {
    return salePrice > 0 && salePrice < regularPrice;
  }

  static validatePriceWithDetails(
    price: number,
    salePrice?: number
  ): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (price <= 0) {
      errors.push("Price must be greater than 0");
    }

    if (salePrice !== undefined) {
      if (salePrice <= 0) {
        errors.push("Sale price must be greater than 0");
      }

      if (salePrice >= price) {
        errors.push("Sale price must be less than regular price");
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static validateStock(stock: number): boolean {
    return Number.isInteger(stock) && stock >= 0;
  }

  static canUpdateProduct(product: ProductEntity, updatedBy: any): boolean {
    // Business logic for who can update products
    return updatedBy.role === "ADMIN" || updatedBy.role === "SUPER_ADMIN";
  }

  static canDeleteProduct(product: ProductEntity, deletedBy: any): boolean {
    // Cannot delete if product has orders
    return deletedBy.role === "SUPER_ADMIN";
  }

  static canDelete(product: ProductEntity): boolean {
    // Business logic for when a product can be deleted
    // For example, cannot delete if it has active orders
    return product.status !== ProductStatus.ACTIVE || product.stock === 0;
  }

  static isOnSale(product: ProductEntity): boolean {
    return (
      product.salePrice !== undefined &&
      product.salePrice !== null &&
      product.salePrice < product.price
    );
  }

  static getDiscountPercentage(product: ProductEntity): number {
    if (!this.isOnSale(product) || !product.salePrice) return 0;
    return Math.round(
      ((product.price - product.salePrice) / product.price) * 100
    );
  }

  static isInStock(product: ProductEntity): boolean {
    return product.stock > 0 && product.status === ProductStatus.ACTIVE;
  }

  static canPurchase(product: ProductEntity, quantity: number): boolean {
    return this.isInStock(product) && product.stock >= quantity;
  }

  static generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();
  }

  static validateSlug(slug: string): boolean {
    const slugRegex = /^[a-z0-9-]+$/;
    return slugRegex.test(slug) && slug.length >= 3 && slug.length <= 100;
  }
}
