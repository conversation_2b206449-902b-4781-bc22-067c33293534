-- Order Module Migration Script
-- This script updates the orders table to match the new schema

-- 1. Add new columns to orders table
ALTER TABLE orders 
ADD COLUMN IF NOT EXISTS order_number VARCHAR(255) UNIQUE,
ADD COLUMN IF NOT EXISTS subtotal DECIMAL(10,2),
ADD COLUMN IF NOT EXISTS tax DECIMAL(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS shipping DECIMAL(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS discount DECIMAL(10,2) DEFAULT 0,
ADD COLUMN IF NOT EXISTS currency VARCHAR(10) DEFAULT 'VND',
ADD COLUMN IF NOT EXISTS tracking_number VARCHAR(255),
ADD COLUMN IF NOT EXISTS shipped_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS delivered_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS cancelled_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS refunded_at TIMESTAMP,
ADD COLUMN IF NOT EXISTS metadata JSONB;

-- 2. Update existing orders to have order numbers and calculated fields
UPDATE orders 
SET order_number = 'ORD' || TO_CHAR(created_at, 'YYMMDD') || LPAD(id::text, 6, '0')
WHERE order_number IS NULL;

UPDATE orders 
SET subtotal = total * 0.9,  -- Assume 90% is subtotal
    tax = total * 0.1        -- Assume 10% is tax
WHERE subtotal IS NULL;

-- 3. Update enum values (if using enum types)
-- Note: This might require recreating the enum or using ALTER TYPE
-- For now, we'll assume the application handles the new enum values

-- 4. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_orders_order_number ON orders(order_number);
CREATE INDEX IF NOT EXISTS idx_orders_user_status ON orders(user_id, status);
CREATE INDEX IF NOT EXISTS idx_orders_payment_status ON orders(payment_status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_orders_tracking_number ON orders(tracking_number);

-- 5. Create indexes on order_items for better joins
CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items(order_id);
CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items(product_id);

-- 6. Verify data integrity
DO $$
BEGIN
    -- Check for orders without order numbers
    IF EXISTS (SELECT 1 FROM orders WHERE order_number IS NULL) THEN
        RAISE NOTICE 'Warning: Found orders without order_number';
    END IF;
    
    -- Check for orders with invalid user_id
    IF EXISTS (SELECT 1 FROM orders WHERE user_id NOT IN (SELECT id FROM users)) THEN
        RAISE NOTICE 'Warning: Found orders with invalid user_id';
    END IF;
    
    -- Check for order items with invalid order_id
    IF EXISTS (SELECT 1 FROM order_items WHERE order_id NOT IN (SELECT id FROM orders)) THEN
        RAISE NOTICE 'Warning: Found order items with invalid order_id';
    END IF;
    
    -- Check for order items with invalid product_id
    IF EXISTS (SELECT 1 FROM order_items WHERE product_id NOT IN (SELECT id FROM products)) THEN
        RAISE NOTICE 'Warning: Found order items with invalid product_id';
    END IF;
END $$;

-- 7. Update order totals to be consistent
UPDATE orders 
SET total = subtotal + tax + shipping - discount
WHERE subtotal IS NOT NULL AND tax IS NOT NULL AND shipping IS NOT NULL AND discount IS NOT NULL;